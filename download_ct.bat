@echo off
echo 开始下载CT图片...

REM 创建images目录
if not exist "images" mkdir images

REM 设置URL前缀
set BASE_URL=https://smp.hzwmin.cn/mh_tjstjyy_tempFile

REM 下载图片
echo 正在下载第1张图片...
curl -L -o "images\ct_001.jpg" "%BASE_URL%/dicom/2025-07-08/CT202506270155/aa3040123e9e4ad38d426e91d836d0b9.jpg"

echo 正在下载第2张图片...
curl -L -o "images\ct_002.jpg" "%BASE_URL%/dicom/2025-07-08/CT202506270155/2182639d9426476c9e77b6a65e48c549.jpg"

echo 正在下载第3张图片...
curl -L -o "images\ct_003.jpg" "%BASE_URL%/dicom/2025-07-08/CT202506270155/168dae35380d4f92b43ce7c363ad016e.jpg"

echo 正在下载第4张图片...
curl -L -o "images\ct_004.jpg" "%BASE_URL%/dicom/2025-07-08/CT202506270155/ffdbf189699540609fef65f4a0e95cc4.jpg"

echo 正在下载第5张图片...
curl -L -o "images\ct_005.jpg" "%BASE_URL%/dicom/2025-07-08/CT202506270155/ebc825503a904499a52d93864425e421.jpg"

echo 正在下载第6张图片...
curl -L -o "images\ct_006.jpg" "%BASE_URL%/dicom/2025-07-08/CT202506270155/d0feae40a9c949e4ae36940fb0a3ffcf.jpg"

echo 正在下载第7张图片...
curl -L -o "images\ct_007.jpg" "%BASE_URL%/dicom/2025-07-08/CT202506270155/e6a51f893d59433b94d56c0bfebc2afc.jpg"

echo 正在下载第8张图片...
curl -L -o "images\ct_008.jpg" "%BASE_URL%/dicom/2025-07-08/CT202506270155/908808b42d5749c8bd874be4ee939b39.jpg"

echo 正在下载第9张图片...
curl -L -o "images\ct_009.jpg" "%BASE_URL%/dicom/2025-07-08/CT202506270155/ca8bd6a21946453d90e6f73c025bcdad.jpg"

echo 正在下载第10张图片...
curl -L -o "images\ct_010.jpg" "%BASE_URL%/dicom/2025-07-08/CT202506270155/d4bddf9319474a299adfbccbf0bed5e5.jpg"

echo 正在下载第11张图片...
curl -L -o "images\ct_011.jpg" "%BASE_URL%/dicom/2025-07-08/CT202506270155/56b033ed74b74810aab2dfdc1672e50e.jpg"

echo 正在下载第12张图片...
curl -L -o "images\ct_012.jpg" "%BASE_URL%/dicom/2025-07-08/CT202506270155/ca48b512ddc84ad79e9dfae2d9d3fd1d.jpg"

echo 正在下载第13张图片...
curl -L -o "images\ct_013.jpg" "%BASE_URL%/dicom/2025-07-08/CT202506270155/48a13c87f9914d28a9f5e76ad2992e04.jpg"

echo 正在下载第14张图片...
curl -L -o "images\ct_014.jpg" "%BASE_URL%/dicom/2025-07-08/CT202506270155/22d13dfae5e44be6b3656571511eb0f5.jpg"

echo 正在下载第15张图片...
curl -L -o "images\ct_015.jpg" "%BASE_URL%/dicom/2025-07-08/CT202506270155/087920a394f645f097a12b9c024d4760.jpg"

echo 正在下载第16张图片...
curl -L -o "images\ct_016.jpg" "%BASE_URL%/dicom/2025-07-08/CT202506270155/585e5802439d46c4ad4b065cef745d44.jpg"

echo 正在下载第17张图片...
curl -L -o "images\ct_017.jpg" "%BASE_URL%/dicom/2025-07-08/CT202506270155/4f69fe799f1843b4a1c0b3b82d1b9cae.jpg"

echo 正在下载第18张图片...
curl -L -o "images\ct_018.jpg" "%BASE_URL%/dicom/2025-07-08/CT202506270155/ede2e835bc8d40eda9aafaa87e5464b5.jpg"

echo 正在下载第19张图片...
curl -L -o "images\ct_019.jpg" "%BASE_URL%/dicom/2025-07-08/CT202506270155/14f663d5337640c19cd93027699e1d54.jpg"

echo 正在下载第20张图片...
curl -L -o "images\ct_020.jpg" "%BASE_URL%/dicom/2025-07-08/CT202506270155/c847a875a85a45abad1d363f8fe35bc1.jpg"

echo 正在下载第21张图片...
curl -L -o "images\ct_021.jpg" "%BASE_URL%/dicom/2025-07-08/CT202506270155/3446de7edcb344c2a71cf95f548e173e.jpg"

echo 正在下载第22张图片...
curl -L -o "images\ct_022.jpg" "%BASE_URL%/dicom/2025-07-08/CT202506270155/155888ba67834469bcf1989ead06322f.jpg"

echo 正在下载第23张图片...
curl -L -o "images\ct_023.jpg" "%BASE_URL%/dicom/2025-07-08/CT202506270155/e5948c8b433648158a8befc922ffb032.jpg"

echo 正在下载第24张图片...
curl -L -o "images\ct_024.jpg" "%BASE_URL%/dicom/2025-07-08/CT202506270155/4254bd7d5f554162b868010dd598450c.jpg"

echo 正在下载第25张图片...
curl -L -o "images\ct_025.jpg" "%BASE_URL%/dicom/2025-07-08/CT202506270155/92978a9447ca4f5497806d2b44fad7b0.jpg"

echo 正在下载第26张图片...
curl -L -o "images\ct_026.jpg" "%BASE_URL%/dicom/2025-07-08/CT202506270155/01ae8900295c49eb94365a14e5ac550d.jpg"

echo 正在下载第27张图片...
curl -L -o "images\ct_027.jpg" "%BASE_URL%/dicom/2025-07-08/CT202506270155/04fc64640aed4c1f9fb8ac7fdde03826.jpg"

echo 正在下载第28张图片...
curl -L -o "images\ct_028.jpg" "%BASE_URL%/dicom/2025-07-08/CT202506270155/9abb04756786417da9a958756c29984d.jpg"

echo 正在下载第29张图片...
curl -L -o "images\ct_029.jpg" "%BASE_URL%/dicom/2025-07-08/CT202506270155/28500335b6864642bc4713a54d17426d.jpg"

echo 正在下载第30张图片...
curl -L -o "images\ct_030.jpg" "%BASE_URL%/dicom/2025-07-08/CT202506270155/1494b9687b2d41e7852c3f1c5090e0ed.jpg"

echo 正在下载第31张图片...
curl -L -o "images\ct_031.jpg" "%BASE_URL%/dicom/2025-07-08/CT202506270155/9d9011e27e5346888de2d3d569869794.jpg"

echo 正在下载第32张图片...
curl -L -o "images\ct_032.jpg" "%BASE_URL%/dicom/2025-07-08/CT202506270155/20b245d44a734d5fa622a15746e740d2.jpg"

echo 正在下载第33张图片...
curl -L -o "images\ct_033.jpg" "%BASE_URL%/dicom/2025-07-08/CT202506270155/bb922b1ab9484058b04dda92406c0752.jpg"

echo 正在下载第34张图片...
curl -L -o "images\ct_034.jpg" "%BASE_URL%/dicom/2025-07-08/CT202506270155/60d0cab4253f4974b992d00c7f56b906.jpg"

echo 正在下载第35张图片...
curl -L -o "images\ct_035.jpg" "%BASE_URL%/dicom/2025-07-08/CT202506270155/d936266ea1904b32bcabe1660d0c009a.jpg"

echo 正在下载第36张图片...
curl -L -o "images\ct_036.jpg" "%BASE_URL%/dicom/2025-07-08/CT202506270155/87d52537455349338ba6b66df4f7d11b.jpg"

echo 正在下载第37张图片...
curl -L -o "images\ct_037.jpg" "%BASE_URL%/dicom/2025-07-08/CT202506270155/cd253906e950445293ac45b6822c24e0.jpg"

echo 正在下载第38张图片...
curl -L -o "images\ct_038.jpg" "%BASE_URL%/dicom/2025-07-08/CT202506270155/38e10a7490d847b5a2bcdb1c263c97d7.jpg"

echo 正在下载第39张图片...
curl -L -o "images\ct_039.jpg" "%BASE_URL%/dicom/2025-07-08/CT202506270155/a847c071900f449e8767e5ff40b764bb.jpg"

echo 正在下载第40张图片...
curl -L -o "images\ct_040.jpg" "%BASE_URL%/dicom/2025-07-08/CT202506270155/ed4d8e78f2d74f93bf7c0e46e644478b.jpg"

echo 下载完成！
echo 图片已保存到 images 文件夹中
pause
