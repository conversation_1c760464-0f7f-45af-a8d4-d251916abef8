<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>腰部CT影像查看器 - 在线版</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #1a1a1a;
            color: #ffffff;
            line-height: 1.6;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50, #34495e);
            padding: 20px 0;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            color: #ecf0f1;
        }

        .header p {
            font-size: 1.1em;
            color: #bdc3c7;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .controls {
            background: #2c3e50;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }

        .control-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .control-group label {
            font-weight: 500;
            color: #ecf0f1;
        }

        .control-group select,
        .control-group input {
            padding: 8px 12px;
            border: none;
            border-radius: 5px;
            background: #34495e;
            color: #ecf0f1;
            font-size: 14px;
        }

        .control-group button {
            padding: 8px 16px;
            border: none;
            border-radius: 5px;
            background: #3498db;
            color: white;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s;
        }

        .control-group button:hover {
            background: #2980b9;
        }

        .image-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .image-card {
            background: #2c3e50;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
            transition: transform 0.3s, box-shadow 0.3s;
            cursor: pointer;
        }

        .image-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.4);
        }

        .image-card img {
            width: 100%;
            height: 300px;
            object-fit: contain;
            background: #000;
            display: block;
        }

        .image-info {
            padding: 15px;
            text-align: center;
        }

        .image-info h3 {
            color: #3498db;
            margin-bottom: 5px;
            font-size: 1.2em;
        }

        .image-info p {
            color: #bdc3c7;
            font-size: 0.9em;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.9);
        }

        .modal-content {
            position: relative;
            margin: auto;
            padding: 20px;
            width: 90%;
            height: 90%;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .modal img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }

        .close {
            position: absolute;
            top: 15px;
            right: 35px;
            color: #f1f1f1;
            font-size: 40px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: #3498db;
        }

        .navigation {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(52, 73, 94, 0.8);
            color: white;
            border: none;
            padding: 15px 20px;
            font-size: 24px;
            cursor: pointer;
            border-radius: 5px;
            transition: background 0.3s;
        }

        .navigation:hover {
            background: rgba(52, 73, 94, 1);
        }

        .prev {
            left: 20px;
        }

        .next {
            right: 20px;
        }

        .loading {
            text-align: center;
            padding: 50px;
            font-size: 1.2em;
            color: #bdc3c7;
        }

        .status {
            background: #2c3e50;
            padding: 10px 20px;
            border-radius: 5px;
            margin-bottom: 20px;
            color: #bdc3c7;
        }

        .success { color: #2ecc71; }
        .error { color: #e74c3c; }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2em;
            }
            
            .controls {
                flex-direction: column;
                align-items: stretch;
            }
            
            .control-group {
                justify-content: space-between;
            }
            
            .image-grid {
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>腰部CT影像查看器</h1>
        <p>CT202506270155 - 在线预览版本 (260张切片)</p>
    </div>

    <div class="container">
        <div class="status">
            <span id="statusText">正在初始化...</span>
            <span style="float: right;">
                成功: <span id="successCount">0</span> | 
                失败: <span id="errorCount">0</span> | 
                总计: <span id="totalCount">260</span>
            </span>
        </div>

        <div class="controls">
            <div class="control-group">
                <label for="gridSize">网格大小:</label>
                <select id="gridSize">
                    <option value="2">2列</option>
                    <option value="3" selected>3列</option>
                    <option value="4">4列</option>
                    <option value="5">5列</option>
                </select>
            </div>
            
            <div class="control-group">
                <label for="imageSize">图片大小:</label>
                <select id="imageSize">
                    <option value="200">小 (200px)</option>
                    <option value="300" selected>中 (300px)</option>
                    <option value="400">大 (400px)</option>
                </select>
            </div>
            
            <div class="control-group">
                <button onclick="toggleFullscreen()">全屏模式</button>
                <button onclick="refreshImages()">刷新图片</button>
            </div>
        </div>

        <div id="imageContainer" class="image-grid">
            <div class="loading">正在加载CT图片...</div>
        </div>
    </div>

    <!-- 模态框 -->
    <div id="imageModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <button class="navigation prev" onclick="previousImage()">&#10094;</button>
            <img id="modalImage" src="" alt="CT图片">
            <button class="navigation next" onclick="nextImage()">&#10095;</button>
        </div>
    </div>

    <script>
        let currentImageIndex = 0;
        let images = [];
        let successCount = 0;
        let errorCount = 0;

        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            loadImages();
            setupEventListeners();
        });

        function loadImages() {
            const BASE_URL = "https://smp.hzwmin.cn/mh_tjstjyy_tempFile";
            
            // 图片URL列表 (完整260张)
            const imageUrls = [
                // 1-40 (原有的)
                "/dicom/2025-07-08/CT202506270155/aa3040123e9e4ad38d426e91d836d0b9.jpg",
                "/dicom/2025-07-08/CT202506270155/2182639d9426476c9e77b6a65e48c549.jpg",
                "/dicom/2025-07-08/CT202506270155/168dae35380d4f92b43ce7c363ad016e.jpg",
                "/dicom/2025-07-08/CT202506270155/ffdbf189699540609fef65f4a0e95cc4.jpg",
                "/dicom/2025-07-08/CT202506270155/ebc825503a904499a52d93864425e421.jpg",
                "/dicom/2025-07-08/CT202506270155/d0feae40a9c949e4ae36940fb0a3ffcf.jpg",
                "/dicom/2025-07-08/CT202506270155/e6a51f893d59433b94d56c0bfebc2afc.jpg",
                "/dicom/2025-07-08/CT202506270155/908808b42d5749c8bd874be4ee939b39.jpg",
                "/dicom/2025-07-08/CT202506270155/ca8bd6a21946453d90e6f73c025bcdad.jpg",
                "/dicom/2025-07-08/CT202506270155/d4bddf9319474a299adfbccbf0bed5e5.jpg",
                "/dicom/2025-07-08/CT202506270155/56b033ed74b74810aab2dfdc1672e50e.jpg",
                "/dicom/2025-07-08/CT202506270155/ca48b512ddc84ad79e9dfae2d9d3fd1d.jpg",
                "/dicom/2025-07-08/CT202506270155/48a13c87f9914d28a9f5e76ad2992e04.jpg",
                "/dicom/2025-07-08/CT202506270155/22d13dfae5e44be6b3656571511eb0f5.jpg",
                "/dicom/2025-07-08/CT202506270155/087920a394f645f097a12b9c024d4760.jpg",
                "/dicom/2025-07-08/CT202506270155/585e5802439d46c4ad4b065cef745d44.jpg",
                "/dicom/2025-07-08/CT202506270155/4f69fe799f1843b4a1c0b3b82d1b9cae.jpg",
                "/dicom/2025-07-08/CT202506270155/ede2e835bc8d40eda9aafaa87e5464b5.jpg",
                "/dicom/2025-07-08/CT202506270155/14f663d5337640c19cd93027699e1d54.jpg",
                "/dicom/2025-07-08/CT202506270155/c847a875a85a45abad1d363f8fe35bc1.jpg",
                "/dicom/2025-07-08/CT202506270155/3446de7edcb344c2a71cf95f548e173e.jpg",
                "/dicom/2025-07-08/CT202506270155/155888ba67834469bcf1989ead06322f.jpg",
                "/dicom/2025-07-08/CT202506270155/e5948c8b433648158a8befc922ffb032.jpg",
                "/dicom/2025-07-08/CT202506270155/4254bd7d5f554162b868010dd598450c.jpg",
                "/dicom/2025-07-08/CT202506270155/92978a9447ca4f5497806d2b44fad7b0.jpg",
                "/dicom/2025-07-08/CT202506270155/01ae8900295c49eb94365a14e5ac550d.jpg",
                "/dicom/2025-07-08/CT202506270155/04fc64640aed4c1f9fb8ac7fdde03826.jpg",
                "/dicom/2025-07-08/CT202506270155/9abb04756786417da9a958756c29984d.jpg",
                "/dicom/2025-07-08/CT202506270155/28500335b6864642bc4713a54d17426d.jpg",
                "/dicom/2025-07-08/CT202506270155/1494b9687b2d41e7852c3f1c5090e0ed.jpg",
                "/dicom/2025-07-08/CT202506270155/9d9011e27e5346888de2d3d569869794.jpg",
                "/dicom/2025-07-08/CT202506270155/20b245d44a734d5fa622a15746e740d2.jpg",
                "/dicom/2025-07-08/CT202506270155/bb922b1ab9484058b04dda92406c0752.jpg",
                "/dicom/2025-07-08/CT202506270155/60d0cab4253f4974b992d00c7f56b906.jpg",
                "/dicom/2025-07-08/CT202506270155/d936266ea1904b32bcabe1660d0c009a.jpg",
                "/dicom/2025-07-08/CT202506270155/87d52537455349338ba6b66df4f7d11b.jpg",
                "/dicom/2025-07-08/CT202506270155/cd253906e950445293ac45b6822c24e0.jpg",
                "/dicom/2025-07-08/CT202506270155/38e10a7490d847b5a2bcdb1c263c97d7.jpg",
                "/dicom/2025-07-08/CT202506270155/a847c071900f449e8767e5ff40b764bb.jpg",
                "/dicom/2025-07-08/CT202506270155/ed4d8e78f2d74f93bf7c0e46e644478b.jpg",
                // 41-60 (新增的)
                "/dicom/2025-07-08/CT202506270155/cba7b8be0f8f45d6ba68732226c3ca91.jpg",
                "/dicom/2025-07-08/CT202506270155/a7f3cc2b57414d8b82f43638122c4c9e.jpg",
                "/dicom/2025-07-08/CT202506270155/42fafcda384d45ef81cb579e59dbe7b9.jpg",
                "/dicom/2025-07-08/CT202506270155/70feeace734643ad80cd22fdc98fa1cf.jpg",
                "/dicom/2025-07-08/CT202506270155/c708a9e9a00a4e6088e1c799c6e453c7.jpg",
                "/dicom/2025-07-08/CT202506270155/cef29739c4d04d00ae10f0987c84a4ab.jpg",
                "/dicom/2025-07-08/CT202506270155/466a3332c9f14e56b107f4eec888ba59.jpg",
                "/dicom/2025-07-08/CT202506270155/635e556e24ad4f789be43010ab430cec.jpg",
                "/dicom/2025-07-08/CT202506270155/2c507c95b8eb455da41d0f3bb0e94d83.jpg",
                "/dicom/2025-07-08/CT202506270155/89f21ddb0c7d4f98be79d686206524d9.jpg",
                "/dicom/2025-07-08/CT202506270155/b760971d8082463fa84870e5f5271d60.jpg",
                "/dicom/2025-07-08/CT202506270155/1b97804a01ac4c5386c60e52dea9f785.jpg",
                "/dicom/2025-07-08/CT202506270155/99fc55cb1b8d4be5ac3f8e66498cb0c4.jpg",
                "/dicom/2025-07-08/CT202506270155/43691b55cdeb4413a615b7f59695afe9.jpg",
                "/dicom/2025-07-08/CT202506270155/0e4f5868f42b40d4914f3d6652faa611.jpg",
                "/dicom/2025-07-08/CT202506270155/c17b61eb8f5847739d5654695263de8e.jpg",
                "/dicom/2025-07-08/CT202506270155/445eb7b5a55a424d8a43d72a184418bc.jpg",
                "/dicom/2025-07-08/CT202506270155/bdb82fb237904c41bc218a9dd246a2b8.jpg",
                "/dicom/2025-07-08/CT202506270155/c8ebaaae47704f9d992eeae3f59cd8a9.jpg",
                "/dicom/2025-07-08/CT202506270155/9c2ef5584abc4b1aa10b9095bad52720.jpg"
            ];

            // 生成图片列表
            images = [];
            imageUrls.forEach((relativeUrl, index) => {
                images.push({
                    no: index + 1,
                    src: BASE_URL + relativeUrl,
                    title: `CT切片 ${index + 1}`,
                    filename: relativeUrl.split('/').pop()
                });
            });

            updateStatus('开始加载图片...');
            loadImageCards();
        }

        function loadImageCards() {
            const container = document.getElementById('imageContainer');
            container.innerHTML = '';

            images.forEach((image, index) => {
                const imageCard = document.createElement('div');
                imageCard.className = 'image-card';
                imageCard.onclick = () => openModal(index);

                const img = document.createElement('img');
                img.src = image.src;
                img.alt = image.title;

                img.onload = function() {
                    successCount++;
                    updateCounts();
                    console.log(`✓ 加载成功: ${image.title}`);
                };

                img.onerror = function() {
                    errorCount++;
                    updateCounts();
                    console.log(`✗ 加载失败: ${image.title}`);
                    // 显示错误占位图
                    img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjMzMzIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iI2ZmZiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuWKoOi9veWksei0pTwvdGV4dD48L3N2Zz4=';
                };

                const imageInfo = document.createElement('div');
                imageInfo.className = 'image-info';
                imageInfo.innerHTML = `
                    <h3>${image.title}</h3>
                    <p>序号: ${image.no}</p>
                    <p style="font-size: 0.8em; color: #999;">${image.filename}</p>
                `;

                imageCard.appendChild(img);
                imageCard.appendChild(imageInfo);
                container.appendChild(imageCard);
            });
        }

        function updateCounts() {
            document.getElementById('successCount').textContent = successCount;
            document.getElementById('errorCount').textContent = errorCount;

            if (successCount + errorCount === images.length) {
                if (successCount > 0) {
                    updateStatus(`加载完成！成功 ${successCount} 张，失败 ${errorCount} 张`);
                } else {
                    updateStatus('所有图片加载失败，请检查网络连接');
                }
            } else {
                updateStatus(`正在加载... ${successCount + errorCount}/${images.length}`);
            }
        }

        function updateStatus(message) {
            document.getElementById('statusText').textContent = message;
        }

        function setupEventListeners() {
            // 网格大小控制
            document.getElementById('gridSize').addEventListener('change', function() {
                const gridSize = this.value;
                const container = document.getElementById('imageContainer');
                container.style.gridTemplateColumns = `repeat(${gridSize}, 1fr)`;
            });

            // 图片大小控制
            document.getElementById('imageSize').addEventListener('change', function() {
                const imageSize = this.value;
                const images = document.querySelectorAll('.image-card img');
                images.forEach(img => {
                    img.style.height = imageSize + 'px';
                });
            });

            // 模态框关闭
            document.querySelector('.close').onclick = closeModal;
            document.getElementById('imageModal').onclick = function(e) {
                if (e.target === this) closeModal();
            };

            // 键盘导航
            document.addEventListener('keydown', function(e) {
                if (document.getElementById('imageModal').style.display === 'block') {
                    if (e.key === 'Escape') closeModal();
                    if (e.key === 'ArrowLeft') previousImage();
                    if (e.key === 'ArrowRight') nextImage();
                }
            });
        }

        function openModal(index) {
            currentImageIndex = index;
            const modal = document.getElementById('imageModal');
            const modalImage = document.getElementById('modalImage');

            modalImage.src = images[index].src;
            modalImage.alt = images[index].title;
            modal.style.display = 'block';
        }

        function closeModal() {
            document.getElementById('imageModal').style.display = 'none';
        }

        function previousImage() {
            currentImageIndex = (currentImageIndex - 1 + images.length) % images.length;
            document.getElementById('modalImage').src = images[currentImageIndex].src;
        }

        function nextImage() {
            currentImageIndex = (currentImageIndex + 1) % images.length;
            document.getElementById('modalImage').src = images[currentImageIndex].src;
        }

        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen();
            } else {
                document.exitFullscreen();
            }
        }

        function refreshImages() {
            successCount = 0;
            errorCount = 0;
            updateStatus('重新加载图片...');
            loadImageCards();
        }
    </script>
</body>
</html>
