<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>腰部CT影像查看器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #1a1a1a;
            color: #ffffff;
            line-height: 1.6;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50, #34495e);
            padding: 20px 0;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            color: #ecf0f1;
        }

        .header p {
            font-size: 1.1em;
            color: #bdc3c7;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .controls {
            background: #2c3e50;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }

        .control-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .control-group label {
            font-weight: 500;
            color: #ecf0f1;
        }

        .control-group select,
        .control-group input {
            padding: 8px 12px;
            border: none;
            border-radius: 5px;
            background: #34495e;
            color: #ecf0f1;
            font-size: 14px;
        }

        .control-group button {
            padding: 8px 16px;
            border: none;
            border-radius: 5px;
            background: #3498db;
            color: white;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s;
        }

        .control-group button:hover {
            background: #2980b9;
        }

        .image-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .image-card {
            background: #2c3e50;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
            transition: transform 0.3s, box-shadow 0.3s;
            cursor: pointer;
        }

        .image-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.4);
        }

        .image-card img {
            width: 100%;
            height: 300px;
            object-fit: contain;
            background: #000;
            display: block;
        }

        .image-info {
            padding: 15px;
            text-align: center;
        }

        .image-info h3 {
            color: #3498db;
            margin-bottom: 5px;
            font-size: 1.2em;
        }

        .image-info p {
            color: #bdc3c7;
            font-size: 0.9em;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.9);
        }

        .modal-content {
            position: relative;
            margin: auto;
            padding: 20px;
            width: 90%;
            height: 90%;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .modal img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }

        .close {
            position: absolute;
            top: 15px;
            right: 35px;
            color: #f1f1f1;
            font-size: 40px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: #3498db;
        }

        .navigation {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(52, 73, 94, 0.8);
            color: white;
            border: none;
            padding: 15px 20px;
            font-size: 24px;
            cursor: pointer;
            border-radius: 5px;
            transition: background 0.3s;
        }

        .navigation:hover {
            background: rgba(52, 73, 94, 1);
        }

        .prev {
            left: 20px;
        }

        .next {
            right: 20px;
        }

        .loading {
            text-align: center;
            padding: 50px;
            font-size: 1.2em;
            color: #bdc3c7;
        }

        .error {
            text-align: center;
            padding: 50px;
            color: #e74c3c;
            font-size: 1.1em;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2em;
            }
            
            .controls {
                flex-direction: column;
                align-items: stretch;
            }
            
            .control-group {
                justify-content: space-between;
            }
            
            .image-grid {
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 15px;
            }
            
            .navigation {
                padding: 10px 15px;
                font-size: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>腰部CT影像查看器</h1>
        <p>CT202506270155 - 专业医学影像展示系统</p>
    </div>

    <div class="container">
        <div class="controls">
            <div class="control-group">
                <label for="gridSize">网格大小:</label>
                <select id="gridSize">
                    <option value="2">2列</option>
                    <option value="3" selected>3列</option>
                    <option value="4">4列</option>
                    <option value="5">5列</option>
                </select>
            </div>
            
            <div class="control-group">
                <label for="imageSize">图片大小:</label>
                <select id="imageSize">
                    <option value="200">小 (200px)</option>
                    <option value="300" selected>中 (300px)</option>
                    <option value="400">大 (400px)</option>
                </select>
            </div>
            
            <div class="control-group">
                <button onclick="toggleFullscreen()">全屏模式</button>
                <button onclick="downloadAll()">下载所有</button>
            </div>
        </div>

        <div id="imageContainer" class="image-grid">
            <div class="loading">正在加载CT图片...</div>
        </div>
    </div>

    <!-- 模态框 -->
    <div id="imageModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <button class="navigation prev" onclick="previousImage()">&#10094;</button>
            <img id="modalImage" src="" alt="CT图片">
            <button class="navigation next" onclick="nextImage()">&#10095;</button>
        </div>
    </div>

    <script>
        let currentImageIndex = 0;
        let images = [];

        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            loadImages();
            setupEventListeners();
        });

        function loadImages() {
            const container = document.getElementById('imageContainer');
            
            // 生成图片列表（1-40）
            images = [];
            for (let i = 1; i <= 40; i++) {
                images.push({
                    no: i,
                    src: `images/ct_${i.toString().padStart(3, '0')}.jpg`,
                    title: `CT切片 ${i}`
                });
            }

            // 清空容器
            container.innerHTML = '';

            // 检查图片是否存在并生成HTML
            let loadedCount = 0;
            let errorCount = 0;

            images.forEach((image, index) => {
                const imageCard = document.createElement('div');
                imageCard.className = 'image-card';
                imageCard.onclick = () => openModal(index);

                const img = document.createElement('img');
                img.src = image.src;
                img.alt = image.title;
                img.onerror = () => {
                    errorCount++;
                    imageCard.style.display = 'none';
                    checkLoadComplete();
                };
                img.onload = () => {
                    loadedCount++;
                    checkLoadComplete();
                };

                const imageInfo = document.createElement('div');
                imageInfo.className = 'image-info';
                imageInfo.innerHTML = `
                    <h3>${image.title}</h3>
                    <p>序号: ${image.no}</p>
                `;

                imageCard.appendChild(img);
                imageCard.appendChild(imageInfo);
                container.appendChild(imageCard);
            });

            function checkLoadComplete() {
                if (loadedCount + errorCount === images.length) {
                    if (loadedCount === 0) {
                        container.innerHTML = '<div class="error">未找到CT图片文件。请先运行下载脚本。</div>';
                    }
                }
            }
        }

        function setupEventListeners() {
            // 网格大小控制
            document.getElementById('gridSize').addEventListener('change', function() {
                const gridSize = this.value;
                const container = document.getElementById('imageContainer');
                container.style.gridTemplateColumns = `repeat(${gridSize}, 1fr)`;
            });

            // 图片大小控制
            document.getElementById('imageSize').addEventListener('change', function() {
                const imageSize = this.value;
                const images = document.querySelectorAll('.image-card img');
                images.forEach(img => {
                    img.style.height = imageSize + 'px';
                });
            });

            // 模态框关闭
            document.querySelector('.close').onclick = closeModal;
            document.getElementById('imageModal').onclick = function(e) {
                if (e.target === this) closeModal();
            };

            // 键盘导航
            document.addEventListener('keydown', function(e) {
                if (document.getElementById('imageModal').style.display === 'block') {
                    if (e.key === 'Escape') closeModal();
                    if (e.key === 'ArrowLeft') previousImage();
                    if (e.key === 'ArrowRight') nextImage();
                }
            });
        }

        function openModal(index) {
            currentImageIndex = index;
            const modal = document.getElementById('imageModal');
            const modalImage = document.getElementById('modalImage');
            
            modalImage.src = images[index].src;
            modalImage.alt = images[index].title;
            modal.style.display = 'block';
        }

        function closeModal() {
            document.getElementById('imageModal').style.display = 'none';
        }

        function previousImage() {
            currentImageIndex = (currentImageIndex - 1 + images.length) % images.length;
            document.getElementById('modalImage').src = images[currentImageIndex].src;
        }

        function nextImage() {
            currentImageIndex = (currentImageIndex + 1) % images.length;
            document.getElementById('modalImage').src = images[currentImageIndex].src;
        }

        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen();
            } else {
                document.exitFullscreen();
            }
        }

        function downloadAll() {
            images.forEach(image => {
                const link = document.createElement('a');
                link.href = image.src;
                link.download = `ct_${image.no.toString().padStart(3, '0')}.jpg`;
                link.click();
            });
        }
    </script>
</body>
</html>
