<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>腰部CT影像查看器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #1a1a1a;
            color: #ffffff;
            line-height: 1.6;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50, #34495e);
            padding: 20px 0;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            color: #ecf0f1;
        }

        .header p {
            font-size: 1.1em;
            color: #bdc3c7;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .controls {
            background: #2c3e50;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }

        .control-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .control-group label {
            font-weight: 500;
            color: #ecf0f1;
        }

        .control-group select,
        .control-group input {
            padding: 8px 12px;
            border: none;
            border-radius: 5px;
            background: #34495e;
            color: #ecf0f1;
            font-size: 14px;
        }

        .control-group button {
            padding: 8px 16px;
            border: none;
            border-radius: 5px;
            background: #3498db;
            color: white;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s;
        }

        .control-group button:hover {
            background: #2980b9;
        }

        .image-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .image-card {
            background: #2c3e50;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
            transition: transform 0.3s, box-shadow 0.3s;
            cursor: pointer;
        }

        .image-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.4);
        }

        .image-card img {
            width: 100%;
            height: 300px;
            object-fit: contain;
            background: #000;
            display: block;
        }

        .image-info {
            padding: 15px;
            text-align: center;
        }

        .image-info h3 {
            color: #3498db;
            margin-bottom: 5px;
            font-size: 1.2em;
        }

        .image-info p {
            color: #bdc3c7;
            font-size: 0.9em;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.9);
        }

        .modal-content {
            position: relative;
            margin: auto;
            padding: 20px;
            width: 90%;
            height: 90%;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .modal img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }

        .close {
            position: absolute;
            top: 15px;
            right: 35px;
            color: #f1f1f1;
            font-size: 40px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: #3498db;
        }

        .navigation {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(52, 73, 94, 0.8);
            color: white;
            border: none;
            padding: 15px 20px;
            font-size: 24px;
            cursor: pointer;
            border-radius: 5px;
            transition: background 0.3s;
        }

        .navigation:hover {
            background: rgba(52, 73, 94, 1);
        }

        .prev {
            left: 20px;
        }

        .next {
            right: 20px;
        }

        .loading {
            text-align: center;
            padding: 50px;
            font-size: 1.2em;
            color: #bdc3c7;
        }

        .error {
            text-align: center;
            padding: 50px;
            color: #e74c3c;
            font-size: 1.1em;
        }

        .context-menu {
            position: fixed;
            background: #2c3e50;
            border: 1px solid #34495e;
            border-radius: 5px;
            padding: 5px 0;
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
            z-index: 2000;
            display: none;
        }

        .context-menu-item {
            padding: 8px 15px;
            color: #ecf0f1;
            cursor: pointer;
            font-size: 14px;
            border-bottom: 1px solid #34495e;
        }

        .context-menu-item:last-child {
            border-bottom: none;
        }

        .context-menu-item:hover {
            background: #34495e;
        }

        .status-bar {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: #2c3e50;
            padding: 10px 20px;
            color: #bdc3c7;
            font-size: 12px;
            border-top: 1px solid #34495e;
            z-index: 1000;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2em;
            }
            
            .controls {
                flex-direction: column;
                align-items: stretch;
            }
            
            .control-group {
                justify-content: space-between;
            }
            
            .image-grid {
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 15px;
            }
            
            .navigation {
                padding: 10px 15px;
                font-size: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>腰部CT影像查看器 - 在线预览版</h1>
        <p>CT202506270155 - 直接从服务器加载图片，无需下载</p>
    </div>

    <div class="container">
        <div class="controls">
            <div class="control-group">
                <label for="gridSize">网格大小:</label>
                <select id="gridSize">
                    <option value="2">2列</option>
                    <option value="3" selected>3列</option>
                    <option value="4">4列</option>
                    <option value="5">5列</option>
                </select>
            </div>
            
            <div class="control-group">
                <label for="imageSize">图片大小:</label>
                <select id="imageSize">
                    <option value="200">小 (200px)</option>
                    <option value="300" selected>中 (300px)</option>
                    <option value="400">大 (400px)</option>
                </select>
            </div>
            
            <div class="control-group">
                <button onclick="toggleFullscreen()">全屏模式</button>
                <button onclick="downloadAll()">下载所有</button>
            </div>
        </div>

        <div id="imageContainer" class="image-grid">
            <div class="loading">正在加载CT图片...</div>
        </div>
    </div>

    <!-- 模态框 -->
    <div id="imageModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <button class="navigation prev" onclick="previousImage()">&#10094;</button>
            <img id="modalImage" src="" alt="CT图片">
            <button class="navigation next" onclick="nextImage()">&#10095;</button>
        </div>
    </div>

    <!-- 右键菜单 -->
    <div id="contextMenu" class="context-menu">
        <div class="context-menu-item" onclick="downloadCurrentImage()">下载此图片</div>
        <div class="context-menu-item" onclick="openImageInNewTab()">在新标签页打开</div>
        <div class="context-menu-item" onclick="copyImageUrl()">复制图片链接</div>
    </div>

    <!-- 状态栏 -->
    <div class="status-bar">
        <span id="statusText">准备加载CT图片...</span>
        <span style="float: right;">
            <span id="imageCount">0/0</span> |
            <span id="loadStatus">等待中</span>
        </span>
    </div>

    <script>
        let currentImageIndex = 0;
        let images = [];
        let currentRightClickIndex = -1;

        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            loadImages();
            setupEventListeners();
            updateStatus('初始化完成，开始加载图片...');
        });

        function loadImages() {
            const container = document.getElementById('imageContainer');

            // URL前缀
            const BASE_URL = "https://smp.hzwmin.cn/mh_tjstjyy_tempFile";

            // 你提供的图片URL列表
            const imageUrls = [
                "/dicom/2025-07-08/CT202506270155/aa3040123e9e4ad38d426e91d836d0b9.jpg",
                "/dicom/2025-07-08/CT202506270155/2182639d9426476c9e77b6a65e48c549.jpg",
                "/dicom/2025-07-08/CT202506270155/168dae35380d4f92b43ce7c363ad016e.jpg",
                "/dicom/2025-07-08/CT202506270155/ffdbf189699540609fef65f4a0e95cc4.jpg",
                "/dicom/2025-07-08/CT202506270155/ebc825503a904499a52d93864425e421.jpg",
                "/dicom/2025-07-08/CT202506270155/d0feae40a9c949e4ae36940fb0a3ffcf.jpg",
                "/dicom/2025-07-08/CT202506270155/e6a51f893d59433b94d56c0bfebc2afc.jpg",
                "/dicom/2025-07-08/CT202506270155/908808b42d5749c8bd874be4ee939b39.jpg",
                "/dicom/2025-07-08/CT202506270155/ca8bd6a21946453d90e6f73c025bcdad.jpg",
                "/dicom/2025-07-08/CT202506270155/d4bddf9319474a299adfbccbf0bed5e5.jpg",
                "/dicom/2025-07-08/CT202506270155/56b033ed74b74810aab2dfdc1672e50e.jpg",
                "/dicom/2025-07-08/CT202506270155/ca48b512ddc84ad79e9dfae2d9d3fd1d.jpg",
                "/dicom/2025-07-08/CT202506270155/48a13c87f9914d28a9f5e76ad2992e04.jpg",
                "/dicom/2025-07-08/CT202506270155/22d13dfae5e44be6b3656571511eb0f5.jpg",
                "/dicom/2025-07-08/CT202506270155/087920a394f645f097a12b9c024d4760.jpg",
                "/dicom/2025-07-08/CT202506270155/585e5802439d46c4ad4b065cef745d44.jpg",
                "/dicom/2025-07-08/CT202506270155/4f69fe799f1843b4a1c0b3b82d1b9cae.jpg",
                "/dicom/2025-07-08/CT202506270155/ede2e835bc8d40eda9aafaa87e5464b5.jpg",
                "/dicom/2025-07-08/CT202506270155/14f663d5337640c19cd93027699e1d54.jpg",
                "/dicom/2025-07-08/CT202506270155/c847a875a85a45abad1d363f8fe35bc1.jpg",
                "/dicom/2025-07-08/CT202506270155/3446de7edcb344c2a71cf95f548e173e.jpg",
                "/dicom/2025-07-08/CT202506270155/155888ba67834469bcf1989ead06322f.jpg",
                "/dicom/2025-07-08/CT202506270155/e5948c8b433648158a8befc922ffb032.jpg",
                "/dicom/2025-07-08/CT202506270155/4254bd7d5f554162b868010dd598450c.jpg",
                "/dicom/2025-07-08/CT202506270155/92978a9447ca4f5497806d2b44fad7b0.jpg",
                "/dicom/2025-07-08/CT202506270155/01ae8900295c49eb94365a14e5ac550d.jpg",
                "/dicom/2025-07-08/CT202506270155/04fc64640aed4c1f9fb8ac7fdde03826.jpg",
                "/dicom/2025-07-08/CT202506270155/9abb04756786417da9a958756c29984d.jpg",
                "/dicom/2025-07-08/CT202506270155/28500335b6864642bc4713a54d17426d.jpg",
                "/dicom/2025-07-08/CT202506270155/1494b9687b2d41e7852c3f1c5090e0ed.jpg",
                "/dicom/2025-07-08/CT202506270155/9d9011e27e5346888de2d3d569869794.jpg",
                "/dicom/2025-07-08/CT202506270155/20b245d44a734d5fa622a15746e740d2.jpg",
                "/dicom/2025-07-08/CT202506270155/bb922b1ab9484058b04dda92406c0752.jpg",
                "/dicom/2025-07-08/CT202506270155/60d0cab4253f4974b992d00c7f56b906.jpg",
                "/dicom/2025-07-08/CT202506270155/d936266ea1904b32bcabe1660d0c009a.jpg",
                "/dicom/2025-07-08/CT202506270155/87d52537455349338ba6b66df4f7d11b.jpg",
                "/dicom/2025-07-08/CT202506270155/cd253906e950445293ac45b6822c24e0.jpg",
                "/dicom/2025-07-08/CT202506270155/38e10a7490d847b5a2bcdb1c263c97d7.jpg",
                "/dicom/2025-07-08/CT202506270155/a847c071900f449e8767e5ff40b764bb.jpg",
                "/dicom/2025-07-08/CT202506270155/ed4d8e78f2d74f93bf7c0e46e644478b.jpg"
            ];

            // 生成图片列表
            images = [];
            imageUrls.forEach((relativeUrl, index) => {
                images.push({
                    no: index + 1,
                    src: BASE_URL + relativeUrl,
                    title: `CT切片 ${index + 1}`,
                    filename: relativeUrl.split('/').pop()
                });
            });

            // 清空容器
            container.innerHTML = '<div class="loading">正在加载CT图片...</div>';

            // 检查图片是否存在并生成HTML
            let loadedCount = 0;
            let errorCount = 0;

            // 延迟一点开始加载，显示加载状态
            setTimeout(() => {
                container.innerHTML = '';

                images.forEach((image, index) => {
                    const imageCard = document.createElement('div');
                    imageCard.className = 'image-card';
                    imageCard.onclick = () => openModal(index);

                    const img = document.createElement('img');
                    img.src = image.src;
                    img.alt = image.title;
                    img.crossOrigin = "anonymous"; // 尝试解决跨域问题

                    img.onerror = () => {
                        errorCount++;
                        // 不隐藏卡片，而是显示错误信息
                        img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjMzMzIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iI2ZmZiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuWKoOi9veWksei0pTwvdGV4dD48L3N2Zz4=';
                        img.style.opacity = '0.5';
                        checkLoadComplete();
                    };

                    img.onload = () => {
                        loadedCount++;
                        checkLoadComplete();
                    };

                    const imageInfo = document.createElement('div');
                    imageInfo.className = 'image-info';
                    imageInfo.innerHTML = `
                        <h3>${image.title}</h3>
                        <p>序号: ${image.no}</p>
                        <p style="font-size: 0.8em; color: #999;">${image.filename}</p>
                    `;

                    imageCard.appendChild(img);
                    imageCard.appendChild(imageInfo);
                    container.appendChild(imageCard);
                });
            }, 500);

            function checkLoadComplete() {
                updateImageCount(loadedCount + errorCount, images.length);

                if (loadedCount + errorCount === images.length) {
                    console.log(`加载完成: 成功 ${loadedCount} 张, 失败 ${errorCount} 张`);
                    updateStatus(`加载完成: 成功 ${loadedCount} 张, 失败 ${errorCount} 张`);
                    updateLoadStatus('完成');

                    if (loadedCount === 0) {
                        container.innerHTML = '<div class="error">所有图片加载失败。可能是网络问题或跨域限制。<br><br>建议：<br>1. 检查网络连接<br>2. 尝试使用VPN<br>3. 直接访问图片链接</div>';
                    }
                } else {
                    updateLoadStatus(`加载中 ${loadedCount + errorCount}/${images.length}`);
                }
            }
        }

        function setupEventListeners() {
            // 网格大小控制
            document.getElementById('gridSize').addEventListener('change', function() {
                const gridSize = this.value;
                const container = document.getElementById('imageContainer');
                container.style.gridTemplateColumns = `repeat(${gridSize}, 1fr)`;
            });

            // 图片大小控制
            document.getElementById('imageSize').addEventListener('change', function() {
                const imageSize = this.value;
                const images = document.querySelectorAll('.image-card img');
                images.forEach(img => {
                    img.style.height = imageSize + 'px';
                });
            });

            // 模态框关闭
            document.querySelector('.close').onclick = closeModal;
            document.getElementById('imageModal').onclick = function(e) {
                if (e.target === this) closeModal();
            };

            // 键盘导航
            document.addEventListener('keydown', function(e) {
                if (document.getElementById('imageModal').style.display === 'block') {
                    if (e.key === 'Escape') closeModal();
                    if (e.key === 'ArrowLeft') previousImage();
                    if (e.key === 'ArrowRight') nextImage();
                }
            });

            // 右键菜单
            document.addEventListener('contextmenu', function(e) {
                const imageCard = e.target.closest('.image-card');
                if (imageCard) {
                    e.preventDefault();

                    // 找到图片索引
                    const allCards = Array.from(document.querySelectorAll('.image-card'));
                    currentRightClickIndex = allCards.indexOf(imageCard);

                    // 显示右键菜单
                    const contextMenu = document.getElementById('contextMenu');
                    contextMenu.style.display = 'block';
                    contextMenu.style.left = e.pageX + 'px';
                    contextMenu.style.top = e.pageY + 'px';
                }
            });

            // 点击其他地方隐藏右键菜单
            document.addEventListener('click', function(e) {
                const contextMenu = document.getElementById('contextMenu');
                if (!contextMenu.contains(e.target)) {
                    contextMenu.style.display = 'none';
                }
            });
        }

        function openModal(index) {
            currentImageIndex = index;
            const modal = document.getElementById('imageModal');
            const modalImage = document.getElementById('modalImage');
            
            modalImage.src = images[index].src;
            modalImage.alt = images[index].title;
            modal.style.display = 'block';
        }

        function closeModal() {
            document.getElementById('imageModal').style.display = 'none';
        }

        function previousImage() {
            currentImageIndex = (currentImageIndex - 1 + images.length) % images.length;
            document.getElementById('modalImage').src = images[currentImageIndex].src;
        }

        function nextImage() {
            currentImageIndex = (currentImageIndex + 1) % images.length;
            document.getElementById('modalImage').src = images[currentImageIndex].src;
        }

        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen();
            } else {
                document.exitFullscreen();
            }
        }

        function downloadAll() {
            if (images.length === 0) {
                alert('没有可下载的图片');
                return;
            }

            const confirmed = confirm(`确定要下载所有 ${images.length} 张CT图片吗？\n\n注意：由于跨域限制，可能需要手动保存图片。`);
            if (!confirmed) return;

            images.forEach((image, index) => {
                setTimeout(() => {
                    try {
                        const link = document.createElement('a');
                        link.href = image.src;
                        link.download = `ct_${image.no.toString().padStart(3, '0')}.jpg`;
                        link.target = '_blank';
                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);
                    } catch (error) {
                        console.error(`下载图片 ${image.no} 失败:`, error);
                    }
                }, index * 200); // 每200ms下载一张，避免浏览器阻止
            });
        }

        function downloadSingle(index) {
            if (index < 0 || index >= images.length) return;

            const image = images[index];
            try {
                const link = document.createElement('a');
                link.href = image.src;
                link.download = `ct_${image.no.toString().padStart(3, '0')}.jpg`;
                link.target = '_blank';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            } catch (error) {
                console.error(`下载图片 ${image.no} 失败:`, error);
                // 如果下载失败，在新窗口打开图片
                window.open(image.src, '_blank');
            }
        }

        // 状态更新函数
        function updateStatus(message) {
            document.getElementById('statusText').textContent = message;
        }

        function updateImageCount(loaded, total) {
            document.getElementById('imageCount').textContent = `${loaded}/${total}`;
        }

        function updateLoadStatus(status) {
            document.getElementById('loadStatus').textContent = status;
        }

        // 右键菜单功能
        function downloadCurrentImage() {
            if (currentRightClickIndex >= 0) {
                downloadSingle(currentRightClickIndex);
            }
            document.getElementById('contextMenu').style.display = 'none';
        }

        function openImageInNewTab() {
            if (currentRightClickIndex >= 0 && currentRightClickIndex < images.length) {
                window.open(images[currentRightClickIndex].src, '_blank');
            }
            document.getElementById('contextMenu').style.display = 'none';
        }

        function copyImageUrl() {
            if (currentRightClickIndex >= 0 && currentRightClickIndex < images.length) {
                const url = images[currentRightClickIndex].src;
                navigator.clipboard.writeText(url).then(() => {
                    updateStatus(`已复制图片链接: ${images[currentRightClickIndex].title}`);
                }).catch(() => {
                    // 如果复制失败，显示URL
                    prompt('复制此链接:', url);
                });
            }
            document.getElementById('contextMenu').style.display = 'none';
        }

        // 添加图片加载进度显示
        function updateImageProgress() {
            const loadedImages = document.querySelectorAll('.image-card img[src]:not([src*="data:image/svg"])').length;
            const totalImages = images.length;
            updateImageCount(loadedImages, totalImages);

            if (loadedImages === totalImages) {
                updateStatus('所有图片加载完成');
                updateLoadStatus('完成');
            } else {
                updateStatus(`正在加载图片... ${loadedImages}/${totalImages}`);
                updateLoadStatus('加载中');
            }
        }
    </script>
</body>
</html>
