<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>腰部CT影像查看器 - 完整版 (500张分页)</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #1a1a1a;
            color: #ffffff;
            line-height: 1.6;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50, #34495e);
            padding: 20px 0;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            color: #ecf0f1;
        }

        .header p {
            font-size: 1.1em;
            color: #bdc3c7;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .controls {
            background: #2c3e50;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .control-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .control-group label {
            font-weight: 500;
            color: #ecf0f1;
            min-width: 80px;
        }

        .control-group select,
        .control-group input {
            flex: 1;
            padding: 8px 12px;
            border: none;
            border-radius: 5px;
            background: #34495e;
            color: #ecf0f1;
            font-size: 14px;
        }

        .control-group button {
            padding: 8px 16px;
            border: none;
            border-radius: 5px;
            background: #3498db;
            color: white;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s;
        }

        .control-group button:hover {
            background: #2980b9;
        }

        .status {
            background: #2c3e50;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            display: grid;
            grid-template-columns: 1fr auto;
            gap: 20px;
            align-items: center;
        }

        .status-left {
            color: #bdc3c7;
        }

        .status-right {
            display: flex;
            gap: 20px;
            font-size: 0.9em;
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 5px;
            margin: 20px 0;
            flex-wrap: wrap;
        }

        .pagination button {
            padding: 8px 12px;
            border: none;
            border-radius: 5px;
            background: #34495e;
            color: #ecf0f1;
            cursor: pointer;
            transition: background 0.3s;
            min-width: 40px;
            font-size: 14px;
        }

        .pagination button:hover:not(:disabled) {
            background: #3498db;
        }

        .pagination button.active {
            background: #3498db;
        }

        .pagination button:disabled {
            background: #2c3e50;
            cursor: not-allowed;
            opacity: 0.5;
        }

        .pagination .page-info {
            color: #bdc3c7;
            margin: 0 10px;
            font-size: 14px;
        }

        .image-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .image-card {
            background: #2c3e50;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
            transition: transform 0.3s, box-shadow 0.3s;
            cursor: pointer;
        }

        .image-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.4);
        }

        .image-card img {
            width: 100%;
            height: 200px;
            object-fit: contain;
            background: #000;
            display: block;
        }

        .image-info {
            padding: 8px;
            text-align: center;
        }

        .image-info h3 {
            color: #3498db;
            margin-bottom: 3px;
            font-size: 0.9em;
        }

        .image-info p {
            color: #bdc3c7;
            font-size: 0.7em;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.95);
        }

        .modal-content {
            position: relative;
            margin: auto;
            padding: 20px;
            width: 95%;
            height: 95%;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .modal img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }

        .modal-info {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(44, 62, 80, 0.9);
            padding: 10px 15px;
            border-radius: 5px;
            color: #ecf0f1;
        }

        .close {
            position: absolute;
            top: 15px;
            right: 35px;
            color: #f1f1f1;
            font-size: 40px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: #3498db;
        }

        .navigation {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(52, 73, 94, 0.8);
            color: white;
            border: none;
            padding: 15px 20px;
            font-size: 24px;
            cursor: pointer;
            border-radius: 5px;
            transition: background 0.3s;
        }

        .navigation:hover {
            background: rgba(52, 73, 94, 1);
        }

        .prev {
            left: 20px;
        }

        .next {
            right: 20px;
        }

        .loading {
            text-align: center;
            padding: 50px;
            font-size: 1.2em;
            color: #bdc3c7;
        }

        .jump-controls {
            background: #34495e;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 15px;
            flex-wrap: wrap;
        }

        .jump-controls h3 {
            color: #ecf0f1;
            margin-right: 10px;
        }

        .jump-input {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .jump-input input {
            width: 80px;
            padding: 5px 8px;
            border: none;
            border-radius: 3px;
            background: #2c3e50;
            color: #ecf0f1;
            text-align: center;
        }

        .jump-input button {
            padding: 5px 15px;
            border: none;
            border-radius: 3px;
            background: #27ae60;
            color: white;
            cursor: pointer;
            transition: background 0.3s;
        }

        .jump-input button:hover {
            background: #219a52;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2em;
            }
            
            .controls {
                grid-template-columns: 1fr;
            }
            
            .image-grid {
                grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
                gap: 10px;
            }

            .image-card img {
                height: 150px;
            }

            .status {
                grid-template-columns: 1fr;
                text-align: center;
            }

            .status-right {
                justify-content: center;
            }

            .jump-controls {
                flex-direction: column;
                align-items: stretch;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>腰部CT影像查看器</h1>
        <p>CT202506270155 - 完整版本 (500张切片分页显示)</p>
    </div>

    <div class="container">
        <div class="status">
            <div class="status-left">
                <span id="statusText">正在初始化...</span>
            </div>
            <div class="status-right">
                <span>页面: <span id="currentPage">1</span>/<span id="totalPages">25</span></span>
                <span>成功: <span id="successCount">0</span></span>
                <span>失败: <span id="errorCount">0</span></span>
                <span>总计: <span id="totalCount">500</span></span>
            </div>
        </div>

        <div class="controls">
            <div class="control-group">
                <label for="imagesPerPage">每页:</label>
                <select id="imagesPerPage">
                    <option value="20" selected>20张</option>
                    <option value="40">40张</option>
                    <option value="60">60张</option>
                    <option value="100">100张</option>
                </select>
            </div>
            
            <div class="control-group">
                <label for="gridSize">网格:</label>
                <select id="gridSize">
                    <option value="4">4列</option>
                    <option value="5" selected>5列</option>
                    <option value="6">6列</option>
                    <option value="8">8列</option>
                </select>
            </div>

            <div class="control-group">
                <label for="imageSize">大小:</label>
                <select id="imageSize">
                    <option value="150">小</option>
                    <option value="200" selected>中</option>
                    <option value="250">大</option>
                </select>
            </div>
            
            <div class="control-group">
                <button onclick="toggleFullscreen()">全屏</button>
            </div>
        </div>

        <div class="jump-controls">
            <h3>快速跳转:</h3>
            <div class="jump-input">
                <label>到第</label>
                <input type="number" id="jumpPage" min="1" max="25" value="1">
                <label>页</label>
                <button onclick="jumpToPage()">跳转</button>
            </div>
            <div class="jump-input">
                <label>到切片</label>
                <input type="number" id="jumpSlice" min="1" max="500" value="1">
                <label>号</label>
                <button onclick="jumpToSlice()">跳转</button>
            </div>
        </div>

        <div class="pagination" id="pagination">
            <!-- 分页控件将在这里动态生成 -->
        </div>

        <div id="imageContainer" class="image-grid">
            <div class="loading">正在加载CT图片...</div>
        </div>

        <div class="pagination" id="paginationBottom">
            <!-- 底部分页控件 -->
        </div>
    </div>

    <!-- 模态框 -->
    <div id="imageModal" class="modal">
        <div class="modal-content">
            <div class="modal-info">
                <div id="modalImageInfo">CT切片信息</div>
            </div>
            <span class="close">&times;</span>
            <button class="navigation prev" onclick="previousImage()">&#10094;</button>
            <img id="modalImage" src="" alt="CT图片">
            <button class="navigation next" onclick="nextImage()">&#10095;</button>
        </div>
    </div>

    <script>
        let currentImageIndex = 0;
        let images = [];
        let successCount = 0;
        let errorCount = 0;
        let currentPage = 1;
        let imagesPerPage = 20;
        let totalPages = 25;
        let allImageUrls = []; // 存储所有500张图片的URL

        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            generateAllImageUrls();
            setupEventListeners();
            loadCurrentPage();
        });

        function generateAllImageUrls() {
            // 完整的500张图片URL映射
            const urlMap = {
                // 1-40: 原始数据
                1: "aa3040123e9e4ad38d426e91d836d0b9.jpg",
                2: "2182639d9426476c9e77b6a65e48c549.jpg",
                3: "168dae35380d4f92b43ce7c363ad016e.jpg",
                4: "ffdbf189699540609fef65f4a0e95cc4.jpg",
                5: "ebc825503a904499a52d93864425e421.jpg",
                6: "d0feae40a9c949e4ae36940fb0a3ffcf.jpg",
                7: "e6a51f893d59433b94d56c0bfebc2afc.jpg",
                8: "908808b42d5749c8bd874be4ee939b39.jpg",
                9: "ca8bd6a21946453d90e6f73c025bcdad.jpg",
                10: "d4bddf9319474a299adfbccbf0bed5e5.jpg",
                11: "56b033ed74b74810aab2dfdc1672e50e.jpg",
                12: "ca48b512ddc84ad79e9dfae2d9d3fd1d.jpg",
                13: "48a13c87f9914d28a9f5e76ad2992e04.jpg",
                14: "22d13dfae5e44be6b3656571511eb0f5.jpg",
                15: "087920a394f645f097a12b9c024d4760.jpg",
                16: "585e5802439d46c4ad4b065cef745d44.jpg",
                17: "4f69fe799f1843b4a1c0b3b82d1b9cae.jpg",
                18: "ede2e835bc8d40eda9aafaa87e5464b5.jpg",
                19: "14f663d5337640c19cd93027699e1d54.jpg",
                20: "c847a875a85a45abad1d363f8fe35bc1.jpg",
                21: "3446de7edcb344c2a71cf95f548e173e.jpg",
                22: "155888ba67834469bcf1989ead06322f.jpg",
                23: "e5948c8b433648158a8befc922ffb032.jpg",
                24: "4254bd7d5f554162b868010dd598450c.jpg",
                25: "92978a9447ca4f5497806d2b44fad7b0.jpg",
                26: "01ae8900295c49eb94365a14e5ac550d.jpg",
                27: "04fc64640aed4c1f9fb8ac7fdde03826.jpg",
                28: "9abb04756786417da9a958756c29984d.jpg",
                29: "28500335b6864642bc4713a54d17426d.jpg",
                30: "1494b9687b2d41e7852c3f1c5090e0ed.jpg",
                31: "9d9011e27e5346888de2d3d569869794.jpg",
                32: "20b245d44a734d5fa622a15746e740d2.jpg",
                33: "bb922b1ab9484058b04dda92406c0752.jpg",
                34: "60d0cab4253f4974b992d00c7f56b906.jpg",
                35: "d936266ea1904b32bcabe1660d0c009a.jpg",
                36: "87d52537455349338ba6b66df4f7d11b.jpg",
                37: "cd253906e950445293ac45b6822c24e0.jpg",
                38: "38e10a7490d847b5a2bcdb1c263c97d7.jpg",
                39: "a847c071900f449e8767e5ff40b764bb.jpg",
                40: "ed4d8e78f2d74f93bf7c0e46e644478b.jpg",
                // 41-60: 第一批新增
                41: "cba7b8be0f8f45d6ba68732226c3ca91.jpg",
                42: "a7f3cc2b57414d8b82f43638122c4c9e.jpg",
                43: "42fafcda384d45ef81cb579e59dbe7b9.jpg",
                44: "70feeace734643ad80cd22fdc98fa1cf.jpg",
                45: "c708a9e9a00a4e6088e1c799c6e453c7.jpg",
                46: "cef29739c4d04d00ae10f0987c84a4ab.jpg",
                47: "466a3332c9f14e56b107f4eec888ba59.jpg",
                48: "635e556e24ad4f789be43010ab430cec.jpg",
                49: "2c507c95b8eb455da41d0f3bb0e94d83.jpg",
                50: "89f21ddb0c7d4f98be79d686206524d9.jpg",
                51: "b760971d8082463fa84870e5f5271d60.jpg",
                52: "1b97804a01ac4c5386c60e52dea9f785.jpg",
                53: "99fc55cb1b8d4be5ac3f8e66498cb0c4.jpg",
                54: "43691b55cdeb4413a615b7f59695afe9.jpg",
                55: "0e4f5868f42b40d4914f3d6652faa611.jpg",
                56: "c17b61eb8f5847739d5654695263de8e.jpg",
                57: "445eb7b5a55a424d8a43d72a184418bc.jpg",
                58: "bdb82fb237904c41bc218a9dd246a2b8.jpg",
                59: "c8ebaaae47704f9d992eeae3f59cd8a9.jpg",
                60: "9c2ef5584abc4b1aa10b9095bad52720.jpg"
            };

            const BASE_URL = "https://smp.hzwmin.cn/mh_tjstjyy_tempFile/dicom/2025-07-08/CT202506270155/";

            allImageUrls = [];
            for (let i = 1; i <= 500; i++) {
                const filename = urlMap[i] || `placeholder_${i.toString().padStart(3, '0')}.jpg`;
                allImageUrls.push({
                    no: i,
                    src: BASE_URL + filename,
                    title: `CT切片 ${i}`,
                    filename: filename
                });
            }

            totalPages = Math.ceil(allImageUrls.length / imagesPerPage);
            updateStatus('图片列表生成完成，准备加载第1页...');
        }

        function loadCurrentPage() {
            const startIndex = (currentPage - 1) * imagesPerPage;
            const endIndex = Math.min(startIndex + imagesPerPage, allImageUrls.length);

            images = allImageUrls.slice(startIndex, endIndex);

            updateStatus(`正在加载第 ${currentPage} 页 (切片 ${startIndex + 1}-${endIndex})...`);
            updatePageInfo();
            setupPagination();
            loadImageCards();
        }

        function loadImageCards() {
            const container = document.getElementById('imageContainer');
            container.innerHTML = '';

            successCount = 0;
            errorCount = 0;

            images.forEach((image, index) => {
                const imageCard = document.createElement('div');
                imageCard.className = 'image-card';
                imageCard.onclick = () => openModal(index);

                const img = document.createElement('img');
                img.src = image.src;
                img.alt = image.title;

                img.onload = function() {
                    successCount++;
                    updateCounts();
                };

                img.onerror = function() {
                    errorCount++;
                    updateCounts();
                    // 显示错误占位图
                    img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjMzMzIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iI2ZmZiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuWKoOi9veWksei0pTwvdGV4dD48L3N2Zz4=';
                };

                const imageInfo = document.createElement('div');
                imageInfo.className = 'image-info';
                imageInfo.innerHTML = `
                    <h3>${image.title}</h3>
                    <p>序号: ${image.no}</p>
                    <p style="font-size: 0.6em; color: #999;">${image.filename}</p>
                `;

                imageCard.appendChild(img);
                imageCard.appendChild(imageInfo);
                container.appendChild(imageCard);
            });
        }

        function updateCounts() {
            document.getElementById('successCount').textContent = successCount;
            document.getElementById('errorCount').textContent = errorCount;

            if (successCount + errorCount === images.length) {
                if (successCount > 0) {
                    updateStatus(`第 ${currentPage} 页加载完成！成功 ${successCount} 张，失败 ${errorCount} 张`);
                } else {
                    updateStatus('当前页面所有图片加载失败，请检查网络连接');
                }
            }
        }

        function updateStatus(message) {
            document.getElementById('statusText').textContent = message;
        }

        function updatePageInfo() {
            document.getElementById('currentPage').textContent = currentPage;
            document.getElementById('totalPages').textContent = totalPages;
            document.getElementById('jumpPage').max = totalPages;
        }

        function setupPagination() {
            const paginationTop = document.getElementById('pagination');
            const paginationBottom = document.getElementById('paginationBottom');

            const paginationHTML = generatePaginationHTML();
            paginationTop.innerHTML = paginationHTML;
            paginationBottom.innerHTML = paginationHTML;
        }

        function generatePaginationHTML() {
            let html = '';

            // 上一页按钮
            html += `<button onclick="goToPage(${currentPage - 1})" ${currentPage <= 1 ? 'disabled' : ''}>‹ 上一页</button>`;

            // 页码按钮
            const startPage = Math.max(1, currentPage - 2);
            const endPage = Math.min(totalPages, currentPage + 2);

            if (startPage > 1) {
                html += `<button onclick="goToPage(1)">1</button>`;
                if (startPage > 2) {
                    html += `<span class="page-info">...</span>`;
                }
            }

            for (let i = startPage; i <= endPage; i++) {
                html += `<button onclick="goToPage(${i})" ${i === currentPage ? 'class="active"' : ''}>${i}</button>`;
            }

            if (endPage < totalPages) {
                if (endPage < totalPages - 1) {
                    html += `<span class="page-info">...</span>`;
                }
                html += `<button onclick="goToPage(${totalPages})">${totalPages}</button>`;
            }

            // 下一页按钮
            html += `<button onclick="goToPage(${currentPage + 1})" ${currentPage >= totalPages ? 'disabled' : ''}>下一页 ›</button>`;

            // 页面信息
            const startSlice = (currentPage - 1) * imagesPerPage + 1;
            const endSlice = Math.min(currentPage * imagesPerPage, 500);
            html += `<span class="page-info">切片 ${startSlice}-${endSlice}</span>`;

            return html;
        }

        function goToPage(page) {
            if (page < 1 || page > totalPages || page === currentPage) return;

            currentPage = page;
            loadCurrentPage();
        }

        function jumpToPage() {
            const page = parseInt(document.getElementById('jumpPage').value);
            if (page >= 1 && page <= totalPages) {
                goToPage(page);
            }
        }

        function jumpToSlice() {
            const slice = parseInt(document.getElementById('jumpSlice').value);
            if (slice >= 1 && slice <= 500) {
                const page = Math.ceil(slice / imagesPerPage);
                goToPage(page);
            }
        }

        function setupEventListeners() {
            // 每页图片数量变化
            document.getElementById('imagesPerPage').addEventListener('change', function() {
                imagesPerPage = parseInt(this.value);
                totalPages = Math.ceil(allImageUrls.length / imagesPerPage);
                currentPage = 1;
                loadCurrentPage();
            });

            // 网格大小控制
            document.getElementById('gridSize').addEventListener('change', function() {
                const gridSize = this.value;
                const container = document.getElementById('imageContainer');
                container.style.gridTemplateColumns = `repeat(${gridSize}, 1fr)`;
            });

            // 图片大小控制
            document.getElementById('imageSize').addEventListener('change', function() {
                const imageSize = this.value;
                const images = document.querySelectorAll('.image-card img');
                images.forEach(img => {
                    img.style.height = imageSize + 'px';
                });
            });

            // 模态框关闭
            document.querySelector('.close').onclick = closeModal;
            document.getElementById('imageModal').onclick = function(e) {
                if (e.target === this) closeModal();
            };

            // 键盘导航
            document.addEventListener('keydown', function(e) {
                if (document.getElementById('imageModal').style.display === 'block') {
                    if (e.key === 'Escape') closeModal();
                    if (e.key === 'ArrowLeft') previousImage();
                    if (e.key === 'ArrowRight') nextImage();
                }
            });

            // 跳转输入框回车事件
            document.getElementById('jumpPage').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') jumpToPage();
            });

            document.getElementById('jumpSlice').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') jumpToSlice();
            });
        }

        function openModal(index) {
            currentImageIndex = index;
            const modal = document.getElementById('imageModal');
            const modalImage = document.getElementById('modalImage');
            const modalInfo = document.getElementById('modalImageInfo');

            const image = images[index];
            modalImage.src = image.src;
            modalImage.alt = image.title;
            modalInfo.innerHTML = `
                <div><strong>${image.title}</strong></div>
                <div>页面: ${currentPage}/${totalPages}</div>
                <div>文件: ${image.filename}</div>
            `;
            modal.style.display = 'block';
        }

        function closeModal() {
            document.getElementById('imageModal').style.display = 'none';
        }

        function previousImage() {
            if (currentImageIndex > 0) {
                currentImageIndex--;
                updateModalImage();
            } else if (currentPage > 1) {
                // 跳转到上一页的最后一张图片
                goToPage(currentPage - 1);
                setTimeout(() => {
                    currentImageIndex = images.length - 1;
                    updateModalImage();
                }, 500);
            }
        }

        function nextImage() {
            if (currentImageIndex < images.length - 1) {
                currentImageIndex++;
                updateModalImage();
            } else if (currentPage < totalPages) {
                // 跳转到下一页的第一张图片
                goToPage(currentPage + 1);
                setTimeout(() => {
                    currentImageIndex = 0;
                    updateModalImage();
                }, 500);
            }
        }

        function updateModalImage() {
            const modalImage = document.getElementById('modalImage');
            const modalInfo = document.getElementById('modalImageInfo');
            const image = images[currentImageIndex];

            modalImage.src = image.src;
            modalImage.alt = image.title;
            modalInfo.innerHTML = `
                <div><strong>${image.title}</strong></div>
                <div>页面: ${currentPage}/${totalPages}</div>
                <div>文件: ${image.filename}</div>
            `;
        }

        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen();
            } else {
                document.exitFullscreen();
            }
        }
    </script>
</body>
</html>
