#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建演示用的CT图片
由于网络问题无法下载真实图片，我们创建一些演示图片来展示网页功能
"""

from PIL import Image, ImageDraw, ImageFont
import os
from pathlib import Path

def create_demo_ct_image(width=512, height=512, slice_number=1):
    """创建一个模拟的CT图片"""
    # 创建黑色背景
    img = Image.new('RGB', (width, height), color='black')
    draw = ImageDraw.Draw(img)
    
    # 绘制模拟的CT扫描图像
    # 外圈 - 模拟身体轮廓
    center_x, center_y = width // 2, height // 2
    outer_radius = min(width, height) // 2 - 20
    
    # 绘制椭圆形身体轮廓
    draw.ellipse([center_x - outer_radius, center_y - outer_radius, 
                  center_x + outer_radius, center_y + outer_radius], 
                 fill='#404040', outline='#606060', width=2)
    
    # 绘制脊椎
    spine_width = 30
    spine_height = outer_radius
    draw.ellipse([center_x - spine_width//2, center_y - spine_height//2,
                  center_x + spine_width//2, center_y + spine_height//2],
                 fill='#808080', outline='#a0a0a0', width=1)
    
    # 绘制肋骨（模拟）
    for i in range(-3, 4):
        if i == 0:
            continue
        rib_y = center_y + i * 40
        rib_radius = outer_radius - abs(i) * 20
        draw.arc([center_x - rib_radius, rib_y - 15,
                  center_x + rib_radius, rib_y + 15],
                 start=0, end=180, fill='#606060', width=2)
    
    # 绘制内脏器官（模拟）
    # 肝脏区域
    if slice_number <= 20:
        liver_x = center_x + 80
        liver_y = center_y - 50
        draw.ellipse([liver_x - 60, liver_y - 40,
                      liver_x + 60, liver_y + 40],
                     fill='#505050', outline='#707070', width=1)
    
    # 肾脏区域
    if 10 <= slice_number <= 30:
        for kidney_side in [-1, 1]:
            kidney_x = center_x + kidney_side * 120
            kidney_y = center_y + 20
            draw.ellipse([kidney_x - 25, kidney_y - 35,
                          kidney_x + 25, kidney_y + 35],
                         fill='#454545', outline='#656565', width=1)
    
    # 添加切片编号
    try:
        # 尝试使用默认字体
        font = ImageFont.load_default()
    except:
        font = None
    
    text = f"CT Slice {slice_number:02d}"
    if font:
        draw.text((10, 10), text, fill='white', font=font)
    else:
        draw.text((10, 10), text, fill='white')
    
    # 添加一些噪点来模拟医学图像
    import random
    for _ in range(100):
        x = random.randint(0, width-1)
        y = random.randint(0, height-1)
        brightness = random.randint(0, 50)
        draw.point((x, y), fill=(brightness, brightness, brightness))
    
    return img

def main():
    """创建演示图片"""
    # 确保images目录存在
    images_dir = Path("images")
    images_dir.mkdir(exist_ok=True)
    
    print("正在创建演示CT图片...")
    
    # 创建40张演示图片
    for i in range(1, 41):
        filename = images_dir / f"ct_{i:03d}.jpg"
        
        if filename.exists():
            print(f"文件已存在，跳过: {filename}")
            continue
        
        print(f"创建图片 {i}/40: {filename}")
        
        # 创建图片
        img = create_demo_ct_image(slice_number=i)
        
        # 保存图片
        img.save(filename, 'JPEG', quality=85)
    
    print(f"\n演示图片创建完成！")
    print(f"共创建了40张CT演示图片")
    print(f"图片保存在: {images_dir.absolute()}")
    print(f"\n现在可以打开 ct_viewer.html 来查看效果")

if __name__ == "__main__":
    main()
