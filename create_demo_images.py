#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建演示用的CT图片
由于网络问题无法下载真实图片，我们创建一些演示图片来展示网页功能
"""

from PIL import Image, ImageDraw, ImageFont
import os
from pathlib import Path

def create_demo_ct_image(width=512, height=512, slice_number=1):
    """创建一个模拟的CT图片"""
    import math
    import random

    # 设置随机种子，确保每个切片都不同但可重现
    random.seed(slice_number * 42)

    # 创建黑色背景
    img = Image.new('RGB', (width, height), color='black')
    draw = ImageDraw.Draw(img)

    # 绘制模拟的CT扫描图像
    center_x, center_y = width // 2, height // 2

    # 根据切片位置调整身体轮廓大小
    slice_ratio = slice_number / 40.0
    base_radius = min(width, height) // 2 - 20

    # 模拟腰部CT的变化：中间切片最大，两端较小
    size_factor = 1.0 - abs(slice_ratio - 0.5) * 0.4
    outer_radius = int(base_radius * size_factor)

    # 身体轮廓颜色随切片变化
    body_brightness = int(40 + slice_number * 2)
    outline_brightness = int(60 + slice_number * 2)

    # 绘制椭圆形身体轮廓（腰部形状）
    ellipse_width = outer_radius
    ellipse_height = int(outer_radius * 0.8)  # 腰部较扁

    draw.ellipse([center_x - ellipse_width, center_y - ellipse_height,
                  center_x + ellipse_width, center_y + ellipse_height],
                 fill=f'#{body_brightness:02x}{body_brightness:02x}{body_brightness:02x}',
                 outline=f'#{outline_brightness:02x}{outline_brightness:02x}{outline_brightness:02x}', width=2)

    # 绘制脊椎（随切片位置变化）
    spine_size = 15 + slice_number // 2
    spine_brightness = int(80 + slice_number * 3)

    # 脊椎位置稍微偏后
    spine_y_offset = int(ellipse_height * 0.3)

    draw.ellipse([center_x - spine_size//2, center_y + spine_y_offset - spine_size//2,
                  center_x + spine_size//2, center_y + spine_y_offset + spine_size//2],
                 fill=f'#{spine_brightness:02x}{spine_brightness:02x}{spine_brightness:02x}',
                 outline='#ffffff', width=1)

    # 绘制椎体结构
    if slice_number % 3 == 0:  # 每3个切片显示椎体
        vertebra_size = spine_size + 10
        draw.rectangle([center_x - vertebra_size//2, center_y + spine_y_offset - 5,
                       center_x + vertebra_size//2, center_y + spine_y_offset + 5],
                      fill=f'#{spine_brightness+20:02x}{spine_brightness+20:02x}{spine_brightness+20:02x}')

    # 绘制内脏器官（根据切片位置显示不同器官）
    organ_brightness = int(45 + slice_number * 1.5)

    # 肾脏区域（在特定切片范围内显示）
    if 8 <= slice_number <= 25:
        kidney_size = 20 + (slice_number - 8) // 2
        for kidney_side in [-1, 1]:
            kidney_x = center_x + kidney_side * (ellipse_width - 40)
            kidney_y = center_y - 10

            # 肾脏形状
            draw.ellipse([kidney_x - kidney_size, kidney_y - kidney_size*1.5,
                          kidney_x + kidney_size, kidney_y + kidney_size*1.5],
                         fill=f'#{organ_brightness:02x}{organ_brightness:02x}{organ_brightness:02x}',
                         outline=f'#{organ_brightness+30:02x}{organ_brightness+30:02x}{organ_brightness+30:02x}', width=1)

    # 肝脏区域（右侧，特定切片）
    if 5 <= slice_number <= 20:
        liver_size = 30 + slice_number
        liver_x = center_x + ellipse_width // 2
        liver_y = center_y - ellipse_height // 3

        draw.ellipse([liver_x - liver_size, liver_y - liver_size//2,
                      liver_x + liver_size//2, liver_y + liver_size//2],
                     fill=f'#{organ_brightness+10:02x}{organ_brightness+10:02x}{organ_brightness+10:02x}',
                     outline=f'#{organ_brightness+40:02x}{organ_brightness+40:02x}{organ_brightness+40:02x}', width=1)

    # 肠道结构（随机分布）
    if slice_number % 2 == 1:
        for _ in range(3 + slice_number // 10):
            intestine_x = center_x + random.randint(-ellipse_width//2, ellipse_width//2)
            intestine_y = center_y + random.randint(-ellipse_height//3, ellipse_height//2)
            intestine_size = random.randint(8, 15)

            draw.ellipse([intestine_x - intestine_size, intestine_y - intestine_size,
                          intestine_x + intestine_size, intestine_y + intestine_size],
                         fill=f'#{organ_brightness-10:02x}{organ_brightness-10:02x}{organ_brightness-10:02x}',
                         outline=f'#{organ_brightness+20:02x}{organ_brightness+20:02x}{organ_brightness+20:02x}', width=1)
    
    # 添加血管结构（细线条）
    vessel_brightness = int(70 + slice_number * 2)
    for _ in range(5 + slice_number // 5):
        start_x = center_x + random.randint(-ellipse_width//2, ellipse_width//2)
        start_y = center_y + random.randint(-ellipse_height//2, ellipse_height//2)
        end_x = start_x + random.randint(-30, 30)
        end_y = start_y + random.randint(-30, 30)

        draw.line([(start_x, start_y), (end_x, end_y)],
                 fill=f'#{vessel_brightness:02x}{vessel_brightness:02x}{vessel_brightness:02x}', width=1)

    # 添加切片编号和患者信息
    try:
        font = ImageFont.load_default()
    except:
        font = None

    # 主标题
    text = f"CT Slice {slice_number:02d}/40"
    if font:
        draw.text((10, 10), text, fill='white', font=font)
    else:
        draw.text((10, 10), text, fill='white')

    # 患者信息
    patient_info = f"L{slice_number} - {slice_number*2.5:.1f}mm"
    if font:
        draw.text((10, height-25), patient_info, fill='#cccccc', font=font)
    else:
        draw.text((10, height-25), patient_info, fill='#cccccc')

    # 添加医学图像特有的噪点和纹理
    for _ in range(200 + slice_number * 5):
        x = random.randint(0, width-1)
        y = random.randint(0, height-1)

        # 在身体区域内添加更多细节
        if (x - center_x)**2 / ellipse_width**2 + (y - center_y)**2 / ellipse_height**2 <= 1:
            brightness = random.randint(20, 80)
        else:
            brightness = random.randint(0, 20)

        draw.point((x, y), fill=(brightness, brightness, brightness))

    # 添加扫描线效果（模拟CT扫描特征）
    if slice_number % 5 == 0:
        for i in range(0, height, 20):
            draw.line([(0, i), (width, i)], fill='#111111', width=1)

    return img

def main():
    """创建演示图片"""
    # 确保images目录存在
    images_dir = Path("images")
    images_dir.mkdir(exist_ok=True)
    
    print("正在创建演示CT图片...")
    
    # 创建40张演示图片
    for i in range(1, 41):
        filename = images_dir / f"ct_{i:03d}.jpg"
        
        if filename.exists():
            print(f"文件已存在，跳过: {filename}")
            continue
        
        print(f"创建图片 {i}/40: {filename}")
        
        # 创建图片
        img = create_demo_ct_image(slice_number=i)
        
        # 保存图片
        img.save(filename, 'JPEG', quality=85)
    
    print(f"\n演示图片创建完成！")
    print(f"共创建了40张CT演示图片")
    print(f"图片保存在: {images_dir.absolute()}")
    print(f"\n现在可以打开 ct_viewer.html 来查看效果")

if __name__ == "__main__":
    main()
