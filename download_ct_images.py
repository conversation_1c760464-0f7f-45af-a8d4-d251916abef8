#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CT图片下载脚本
从API返回的JSON数据中提取图片URL，拼接前缀并下载到本地
"""

import json
import requests
import os
from pathlib import Path
import time
import urllib3
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# URL前缀
BASE_URL = "https://smp.hzwmin.cn/mh_tjstjyy_tempFile"

# 你提供的JSON数据
json_data = [
    {
        "msg": "查询成功",
        "code": "0000",
        "command": "700018",
        "object": {
            "images": [
                {"no": 1, "url": "/dicom/2025-07-08/CT202506270155/aa3040123e9e4ad38d426e91d836d0b9.jpg"},
                {"no": 2, "url": "/dicom/2025-07-08/CT202506270155/2182639d9426476c9e77b6a65e48c549.jpg"},
                {"no": 3, "url": "/dicom/2025-07-08/CT202506270155/168dae35380d4f92b43ce7c363ad016e.jpg"},
                {"no": 4, "url": "/dicom/2025-07-08/CT202506270155/ffdbf189699540609fef65f4a0e95cc4.jpg"},
                {"no": 5, "url": "/dicom/2025-07-08/CT202506270155/ebc825503a904499a52d93864425e421.jpg"},
                {"no": 6, "url": "/dicom/2025-07-08/CT202506270155/d0feae40a9c949e4ae36940fb0a3ffcf.jpg"},
                {"no": 7, "url": "/dicom/2025-07-08/CT202506270155/e6a51f893d59433b94d56c0bfebc2afc.jpg"},
                {"no": 8, "url": "/dicom/2025-07-08/CT202506270155/908808b42d5749c8bd874be4ee939b39.jpg"},
                {"no": 9, "url": "/dicom/2025-07-08/CT202506270155/ca8bd6a21946453d90e6f73c025bcdad.jpg"},
                {"no": 10, "url": "/dicom/2025-07-08/CT202506270155/d4bddf9319474a299adfbccbf0bed5e5.jpg"},
                {"no": 11, "url": "/dicom/2025-07-08/CT202506270155/56b033ed74b74810aab2dfdc1672e50e.jpg"},
                {"no": 12, "url": "/dicom/2025-07-08/CT202506270155/ca48b512ddc84ad79e9dfae2d9d3fd1d.jpg"},
                {"no": 13, "url": "/dicom/2025-07-08/CT202506270155/48a13c87f9914d28a9f5e76ad2992e04.jpg"},
                {"no": 14, "url": "/dicom/2025-07-08/CT202506270155/22d13dfae5e44be6b3656571511eb0f5.jpg"},
                {"no": 15, "url": "/dicom/2025-07-08/CT202506270155/087920a394f645f097a12b9c024d4760.jpg"},
                {"no": 16, "url": "/dicom/2025-07-08/CT202506270155/585e5802439d46c4ad4b065cef745d44.jpg"},
                {"no": 17, "url": "/dicom/2025-07-08/CT202506270155/4f69fe799f1843b4a1c0b3b82d1b9cae.jpg"},
                {"no": 18, "url": "/dicom/2025-07-08/CT202506270155/ede2e835bc8d40eda9aafaa87e5464b5.jpg"},
                {"no": 19, "url": "/dicom/2025-07-08/CT202506270155/14f663d5337640c19cd93027699e1d54.jpg"},
                {"no": 20, "url": "/dicom/2025-07-08/CT202506270155/c847a875a85a45abad1d363f8fe35bc1.jpg"}
            ],
            "totalCount": "500"
        },
        "status": True
    },
    {
        "msg": "查询成功",
        "code": "0000",
        "command": "700018",
        "object": {
            "images": [
                {"no": 21, "url": "/dicom/2025-07-08/CT202506270155/3446de7edcb344c2a71cf95f548e173e.jpg"},
                {"no": 22, "url": "/dicom/2025-07-08/CT202506270155/155888ba67834469bcf1989ead06322f.jpg"},
                {"no": 23, "url": "/dicom/2025-07-08/CT202506270155/e5948c8b433648158a8befc922ffb032.jpg"},
                {"no": 24, "url": "/dicom/2025-07-08/CT202506270155/4254bd7d5f554162b868010dd598450c.jpg"},
                {"no": 25, "url": "/dicom/2025-07-08/CT202506270155/92978a9447ca4f5497806d2b44fad7b0.jpg"},
                {"no": 26, "url": "/dicom/2025-07-08/CT202506270155/01ae8900295c49eb94365a14e5ac550d.jpg"},
                {"no": 27, "url": "/dicom/2025-07-08/CT202506270155/04fc64640aed4c1f9fb8ac7fdde03826.jpg"},
                {"no": 28, "url": "/dicom/2025-07-08/CT202506270155/9abb04756786417da9a958756c29984d.jpg"},
                {"no": 29, "url": "/dicom/2025-07-08/CT202506270155/28500335b6864642bc4713a54d17426d.jpg"},
                {"no": 30, "url": "/dicom/2025-07-08/CT202506270155/1494b9687b2d41e7852c3f1c5090e0ed.jpg"},
                {"no": 31, "url": "/dicom/2025-07-08/CT202506270155/9d9011e27e5346888de2d3d569869794.jpg"},
                {"no": 32, "url": "/dicom/2025-07-08/CT202506270155/20b245d44a734d5fa622a15746e740d2.jpg"},
                {"no": 33, "url": "/dicom/2025-07-08/CT202506270155/bb922b1ab9484058b04dda92406c0752.jpg"},
                {"no": 34, "url": "/dicom/2025-07-08/CT202506270155/60d0cab4253f4974b992d00c7f56b906.jpg"},
                {"no": 35, "url": "/dicom/2025-07-08/CT202506270155/d936266ea1904b32bcabe1660d0c009a.jpg"},
                {"no": 36, "url": "/dicom/2025-07-08/CT202506270155/87d52537455349338ba6b66df4f7d11b.jpg"},
                {"no": 37, "url": "/dicom/2025-07-08/CT202506270155/cd253906e950445293ac45b6822c24e0.jpg"},
                {"no": 38, "url": "/dicom/2025-07-08/CT202506270155/38e10a7490d847b5a2bcdb1c263c97d7.jpg"},
                {"no": 39, "url": "/dicom/2025-07-08/CT202506270155/a847c071900f449e8767e5ff40b764bb.jpg"},
                {"no": 40, "url": "/dicom/2025-07-08/CT202506270155/ed4d8e78f2d74f93bf7c0e46e644478b.jpg"}
            ],
            "totalCount": "500"
        },
        "status": True
    }
]

def create_session():
    """创建带有重试机制的会话"""
    session = requests.Session()

    # 配置重试策略
    retry_strategy = Retry(
        total=3,
        backoff_factor=1,
        status_forcelist=[429, 500, 502, 503, 504],
    )
    adapter = HTTPAdapter(max_retries=retry_strategy)
    session.mount("http://", adapter)
    session.mount("https://", adapter)

    # 设置请求头
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    })

    return session

def download_image(session, url, filename, max_retries=3):
    """下载单个图片文件"""
    for attempt in range(max_retries):
        try:
            print(f"正在下载: {filename} (尝试 {attempt + 1}/{max_retries})")
            response = session.get(url, timeout=30, verify=False)
            response.raise_for_status()

            with open(filename, 'wb') as f:
                f.write(response.content)

            print(f"✓ 下载成功: {filename}")
            return True

        except Exception as e:
            print(f"✗ 下载失败: {filename}, 错误: {e}")
            if attempt < max_retries - 1:
                time.sleep(2)  # 等待2秒后重试

    return False

def main():
    """主函数"""
    # 确保images目录存在
    images_dir = Path("images")
    images_dir.mkdir(exist_ok=True)

    # 创建会话
    session = create_session()

    # 收集所有图片信息
    all_images = []
    for data in json_data:
        if data.get("status") and "object" in data and "images" in data["object"]:
            all_images.extend(data["object"]["images"])

    print(f"总共找到 {len(all_images)} 张CT图片")

    # 下载图片
    success_count = 0
    failed_count = 0

    for image_info in all_images:
        no = image_info["no"]
        relative_url = image_info["url"]

        # 拼接完整URL
        full_url = BASE_URL + relative_url

        # 生成本地文件名
        filename = images_dir / f"ct_{no:03d}.jpg"

        # 如果文件已存在，跳过下载
        if filename.exists():
            print(f"文件已存在，跳过: {filename}")
            success_count += 1
            continue

        # 下载图片
        if download_image(session, full_url, filename):
            success_count += 1
        else:
            failed_count += 1

        # 添加小延迟避免请求过于频繁
        time.sleep(0.5)

    print(f"\n下载完成!")
    print(f"成功: {success_count} 张")
    print(f"失败: {failed_count} 张")
    print(f"图片保存在: {images_dir.absolute()}")

    # 关闭会话
    session.close()

if __name__ == "__main__":
    main()
