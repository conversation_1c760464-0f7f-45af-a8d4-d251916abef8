#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证生成的CT图片是否具有多样性
"""

import os
import hashlib
from pathlib import Path

def get_file_hash(filepath):
    """获取文件的MD5哈希值"""
    hash_md5 = hashlib.md5()
    with open(filepath, "rb") as f:
        for chunk in iter(lambda: f.read(4096), b""):
            hash_md5.update(chunk)
    return hash_md5.hexdigest()

def verify_image_diversity():
    """验证图片的多样性"""
    images_dir = Path("images")
    
    if not images_dir.exists():
        print("❌ images目录不存在")
        return
    
    # 获取所有CT图片
    ct_images = list(images_dir.glob("ct_*.jpg"))
    ct_images.sort()
    
    if not ct_images:
        print("❌ 没有找到CT图片")
        return
    
    print(f"📊 找到 {len(ct_images)} 张CT图片")
    print("=" * 50)
    
    # 计算每个图片的哈希值
    hashes = {}
    file_sizes = {}
    
    for img_path in ct_images:
        try:
            file_hash = get_file_hash(img_path)
            file_size = img_path.stat().st_size
            
            hashes[img_path.name] = file_hash
            file_sizes[img_path.name] = file_size
            
            print(f"✅ {img_path.name}: {file_size:,} bytes, hash: {file_hash[:8]}...")
            
        except Exception as e:
            print(f"❌ {img_path.name}: 读取失败 - {e}")
    
    print("=" * 50)
    
    # 检查重复
    hash_counts = {}
    for filename, file_hash in hashes.items():
        if file_hash in hash_counts:
            hash_counts[file_hash].append(filename)
        else:
            hash_counts[file_hash] = [filename]
    
    # 统计结果
    unique_images = len(hash_counts)
    total_images = len(hashes)
    duplicate_groups = [files for files in hash_counts.values() if len(files) > 1]
    
    print(f"📈 统计结果:")
    print(f"   总图片数: {total_images}")
    print(f"   唯一图片: {unique_images}")
    print(f"   重复组数: {len(duplicate_groups)}")
    
    if duplicate_groups:
        print(f"\n⚠️  发现重复图片:")
        for i, group in enumerate(duplicate_groups, 1):
            print(f"   组 {i}: {', '.join(group)}")
    else:
        print(f"\n🎉 所有图片都是唯一的！")
    
    # 文件大小分析
    sizes = list(file_sizes.values())
    if sizes:
        min_size = min(sizes)
        max_size = max(sizes)
        avg_size = sum(sizes) / len(sizes)
        
        print(f"\n📏 文件大小分析:")
        print(f"   最小: {min_size:,} bytes")
        print(f"   最大: {max_size:,} bytes")
        print(f"   平均: {avg_size:,.0f} bytes")
        print(f"   差异: {max_size - min_size:,} bytes")
    
    # 多样性评分
    diversity_score = (unique_images / total_images) * 100 if total_images > 0 else 0
    
    print(f"\n🏆 多样性评分: {diversity_score:.1f}%")
    
    if diversity_score == 100:
        print("   评级: 优秀 ⭐⭐⭐⭐⭐")
    elif diversity_score >= 90:
        print("   评级: 良好 ⭐⭐⭐⭐")
    elif diversity_score >= 70:
        print("   评级: 一般 ⭐⭐⭐")
    else:
        print("   评级: 需要改进 ⭐⭐")

if __name__ == "__main__":
    verify_image_diversity()
