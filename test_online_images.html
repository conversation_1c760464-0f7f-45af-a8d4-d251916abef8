<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CT图片在线加载测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a1a;
            color: white;
            margin: 0;
            padding: 20px;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .test-image {
            display: inline-block;
            margin: 10px;
            border: 2px solid #333;
            border-radius: 5px;
            overflow: hidden;
            background: #333;
        }
        .test-image img {
            width: 200px;
            height: 200px;
            object-fit: contain;
            background: #000;
        }
        .test-image .info {
            padding: 10px;
            text-align: center;
            font-size: 12px;
            background: #2c3e50;
        }
        .status {
            background: #2c3e50;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .success { color: #2ecc71; }
        .error { color: #e74c3c; }
        .loading { color: #f39c12; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>CT图片在线加载测试</h1>
        
        <div class="status">
            <h3>测试状态</h3>
            <p id="statusText" class="loading">正在测试图片加载...</p>
            <p>成功: <span id="successCount">0</span> | 失败: <span id="errorCount">0</span> | 总计: <span id="totalCount">0</span></p>
        </div>

        <div id="imageContainer">
            <!-- 图片将在这里动态加载 -->
        </div>
    </div>

    <script>
        const BASE_URL = "https://smp.hzwmin.cn/mh_tjstjyy_tempFile";
        
        // 测试前5张图片
        const testUrls = [
            "/dicom/2025-07-08/CT202506270155/aa3040123e9e4ad38d426e91d836d0b9.jpg",
            "/dicom/2025-07-08/CT202506270155/2182639d9426476c9e77b6a65e48c549.jpg",
            "/dicom/2025-07-08/CT202506270155/168dae35380d4f92b43ce7c363ad016e.jpg",
            "/dicom/2025-07-08/CT202506270155/ffdbf189699540609fef65f4a0e95cc4.jpg",
            "/dicom/2025-07-08/CT202506270155/ebc825503a904499a52d93864425e421.jpg"
        ];

        let successCount = 0;
        let errorCount = 0;
        let totalCount = testUrls.length;

        function updateStatus() {
            document.getElementById('successCount').textContent = successCount;
            document.getElementById('errorCount').textContent = errorCount;
            document.getElementById('totalCount').textContent = totalCount;

            const statusText = document.getElementById('statusText');
            if (successCount + errorCount === totalCount) {
                if (successCount > 0) {
                    statusText.textContent = `测试完成！成功加载 ${successCount} 张图片`;
                    statusText.className = 'success';
                } else {
                    statusText.textContent = '测试完成，但所有图片都加载失败';
                    statusText.className = 'error';
                }
            } else {
                statusText.textContent = `正在测试... ${successCount + errorCount}/${totalCount}`;
                statusText.className = 'loading';
            }
        }

        function testImageLoad() {
            const container = document.getElementById('imageContainer');
            
            testUrls.forEach((relativeUrl, index) => {
                const fullUrl = BASE_URL + relativeUrl;
                const filename = relativeUrl.split('/').pop();
                
                // 创建测试容器
                const testDiv = document.createElement('div');
                testDiv.className = 'test-image';
                
                // 创建图片元素
                const img = document.createElement('img');
                img.src = fullUrl;
                img.alt = `CT图片 ${index + 1}`;
                
                // 创建信息区域
                const info = document.createElement('div');
                info.className = 'info';
                info.innerHTML = `
                    <div>图片 ${index + 1}</div>
                    <div style="font-size: 10px; color: #bdc3c7;">${filename}</div>
                    <div id="status-${index}" style="margin-top: 5px;">加载中...</div>
                `;
                
                // 图片加载成功
                img.onload = function() {
                    successCount++;
                    document.getElementById(`status-${index}`).innerHTML = '<span style="color: #2ecc71;">✓ 成功</span>';
                    updateStatus();
                };
                
                // 图片加载失败
                img.onerror = function() {
                    errorCount++;
                    document.getElementById(`status-${index}`).innerHTML = '<span style="color: #e74c3c;">✗ 失败</span>';
                    img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjMzMzIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iI2ZmZiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuWKoOi9veWksei0pTwvdGV4dD48L3N2Zz4=';
                    updateStatus();
                };
                
                testDiv.appendChild(img);
                testDiv.appendChild(info);
                container.appendChild(testDiv);
            });
        }

        // 页面加载完成后开始测试
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus();
            testImageLoad();
        });
    </script>
</body>
</html>
