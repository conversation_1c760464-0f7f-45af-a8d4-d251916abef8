# 腰部CT影像查看器

这是一个专业的医学影像展示系统，用于查看和管理腰部CT扫描图片。

## 功能特点

### 🖼️ 图片展示
- **网格布局**: 支持2-5列的自适应网格显示
- **响应式设计**: 自动适配不同屏幕尺寸
- **图片缩放**: 支持小、中、大三种显示尺寸
- **模态框查看**: 点击图片可全屏查看
- **键盘导航**: 支持方向键切换图片

### 🎛️ 交互控制
- **全屏模式**: 支持浏览器全屏显示
- **批量下载**: 一键下载所有图片
- **图片导航**: 上一张/下一张快速切换
- **ESC退出**: 按ESC键退出模态框

### 🎨 专业界面
- **医学主题**: 深色背景，适合医学影像查看
- **专业配色**: 蓝灰色调，减少眼部疲劳
- **清晰标识**: 每张图片都有序号和标题
- **状态提示**: 加载状态和错误提示

## 文件结构

```
waist ct/
├── ct_viewer.html              # 主要的网页查看器
├── images/                     # CT图片存储目录
│   ├── ct_001.jpg             # CT切片图片 (001-040)
│   ├── ct_002.jpg
│   └── ...
├── download_ct_images.py       # Python下载脚本
├── download_ct.bat            # 批处理下载脚本
├── download_ct_curl.ps1       # PowerShell下载脚本
├── create_demo_images.py      # 创建演示图片脚本
└── README.md                  # 说明文档
```

## 使用方法

### 1. 查看CT图片
直接打开 `ct_viewer.html` 文件即可开始使用：
- 双击文件或在浏览器中打开
- 图片会自动加载并显示在网格中
- 点击任意图片可全屏查看

### 2. 下载真实CT图片
如果你有真实的CT图片URL，可以使用以下方法下载：

#### 方法1: Python脚本 (推荐)
```bash
python download_ct_images.py
```

#### 方法2: 批处理文件
```bash
download_ct.bat
```

#### 方法3: PowerShell脚本
```powershell
powershell -ExecutionPolicy Bypass -File download_ct_curl.ps1
```

### 3. 创建演示图片
如果无法下载真实图片，可以创建演示图片：
```bash
python create_demo_images.py
```

## 技术特性

### 前端技术
- **HTML5**: 现代网页标准
- **CSS3**: 响应式布局和动画效果
- **JavaScript**: 交互功能和图片管理
- **无依赖**: 不需要额外的框架或库

### 兼容性
- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 12+
- ✅ Edge 79+

### 响应式设计
- 📱 手机端: 1-2列显示
- 📱 平板端: 2-3列显示
- 💻 桌面端: 3-5列显示

## 快捷键

| 快捷键 | 功能 |
|--------|------|
| `点击图片` | 打开全屏查看 |
| `ESC` | 退出全屏模式 |
| `←` | 上一张图片 |
| `→` | 下一张图片 |
| `F11` | 浏览器全屏 |

## 自定义配置

### 修改图片数量
如果你的CT图片数量不是40张，请修改 `ct_viewer.html` 中的以下代码：
```javascript
// 第85行附近
for (let i = 1; i <= 40; i++) {  // 修改这里的数字
```

### 修改图片命名规则
如果你的图片命名不是 `ct_001.jpg` 格式，请修改：
```javascript
// 第89行附近
src: `images/ct_${i.toString().padStart(3, '0')}.jpg`,
```

### 修改显示标题
在 `ct_viewer.html` 的第20-22行修改标题：
```html
<h1>腰部CT影像查看器</h1>
<p>CT202506270155 - 专业医学影像展示系统</p>
```

## 故障排除

### 图片无法显示
1. 检查 `images` 文件夹是否存在
2. 确认图片文件名格式正确 (`ct_001.jpg` 到 `ct_040.jpg`)
3. 检查图片文件是否损坏

### 下载失败
1. 检查网络连接
2. 确认URL地址正确
3. 尝试使用不同的下载方法

### 浏览器兼容性问题
1. 使用现代浏览器 (Chrome, Firefox, Safari, Edge)
2. 启用JavaScript
3. 清除浏览器缓存

## 许可证

本项目仅供学习和研究使用。请遵守相关的医学影像使用规定。

## 更新日志

### v1.0.0 (2025-07-09)
- ✨ 初始版本发布
- 🎨 专业医学影像界面设计
- 📱 响应式布局支持
- 🖼️ 模态框全屏查看
- ⌨️ 键盘导航支持
- 📥 多种下载方式
- 🎭 演示图片生成功能
