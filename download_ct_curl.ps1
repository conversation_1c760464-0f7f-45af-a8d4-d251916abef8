# PowerShell脚本：使用curl下载CT图片
# 这个脚本使用curl命令下载图片，避免Python网络配置问题

# URL前缀
$BASE_URL = "https://smp.hzwmin.cn/mh_tjstjyy_tempFile"

# 确保images目录存在
if (!(Test-Path "images")) {
    New-Item -ItemType Directory -Path "images"
}

# 图片URL列表（从JSON数据中提取）
$imageUrls = @(
    "/dicom/2025-07-08/CT202506270155/aa3040123e9e4ad38d426e91d836d0b9.jpg",
    "/dicom/2025-07-08/CT202506270155/2182639d9426476c9e77b6a65e48c549.jpg",
    "/dicom/2025-07-08/CT202506270155/168dae35380d4f92b43ce7c363ad016e.jpg",
    "/dicom/2025-07-08/CT202506270155/ffdbf189699540609fef65f4a0e95cc4.jpg",
    "/dicom/2025-07-08/CT202506270155/ebc825503a904499a52d93864425e421.jpg",
    "/dicom/2025-07-08/CT202506270155/d0feae40a9c949e4ae36940fb0a3ffcf.jpg",
    "/dicom/2025-07-08/CT202506270155/e6a51f893d59433b94d56c0bfebc2afc.jpg",
    "/dicom/2025-07-08/CT202506270155/908808b42d5749c8bd874be4ee939b39.jpg",
    "/dicom/2025-07-08/CT202506270155/ca8bd6a21946453d90e6f73c025bcdad.jpg",
    "/dicom/2025-07-08/CT202506270155/d4bddf9319474a299adfbccbf0bed5e5.jpg",
    "/dicom/2025-07-08/CT202506270155/56b033ed74b74810aab2dfdc1672e50e.jpg",
    "/dicom/2025-07-08/CT202506270155/ca48b512ddc84ad79e9dfae2d9d3fd1d.jpg",
    "/dicom/2025-07-08/CT202506270155/48a13c87f9914d28a9f5e76ad2992e04.jpg",
    "/dicom/2025-07-08/CT202506270155/22d13dfae5e44be6b3656571511eb0f5.jpg",
    "/dicom/2025-07-08/CT202506270155/087920a394f645f097a12b9c024d4760.jpg",
    "/dicom/2025-07-08/CT202506270155/585e5802439d46c4ad4b065cef745d44.jpg",
    "/dicom/2025-07-08/CT202506270155/4f69fe799f1843b4a1c0b3b82d1b9cae.jpg",
    "/dicom/2025-07-08/CT202506270155/ede2e835bc8d40eda9aafaa87e5464b5.jpg",
    "/dicom/2025-07-08/CT202506270155/14f663d5337640c19cd93027699e1d54.jpg",
    "/dicom/2025-07-08/CT202506270155/c847a875a85a45abad1d363f8fe35bc1.jpg",
    "/dicom/2025-07-08/CT202506270155/3446de7edcb344c2a71cf95f548e173e.jpg",
    "/dicom/2025-07-08/CT202506270155/155888ba67834469bcf1989ead06322f.jpg",
    "/dicom/2025-07-08/CT202506270155/e5948c8b433648158a8befc922ffb032.jpg",
    "/dicom/2025-07-08/CT202506270155/4254bd7d5f554162b868010dd598450c.jpg",
    "/dicom/2025-07-08/CT202506270155/92978a9447ca4f5497806d2b44fad7b0.jpg",
    "/dicom/2025-07-08/CT202506270155/01ae8900295c49eb94365a14e5ac550d.jpg",
    "/dicom/2025-07-08/CT202506270155/04fc64640aed4c1f9fb8ac7fdde03826.jpg",
    "/dicom/2025-07-08/CT202506270155/9abb04756786417da9a958756c29984d.jpg",
    "/dicom/2025-07-08/CT202506270155/28500335b6864642bc4713a54d17426d.jpg",
    "/dicom/2025-07-08/CT202506270155/1494b9687b2d41e7852c3f1c5090e0ed.jpg",
    "/dicom/2025-07-08/CT202506270155/9d9011e27e5346888de2d3d569869794.jpg",
    "/dicom/2025-07-08/CT202506270155/20b245d44a734d5fa622a15746e740d2.jpg",
    "/dicom/2025-07-08/CT202506270155/bb922b1ab9484058b04dda92406c0752.jpg",
    "/dicom/2025-07-08/CT202506270155/60d0cab4253f4974b992d00c7f56b906.jpg",
    "/dicom/2025-07-08/CT202506270155/d936266ea1904b32bcabe1660d0c009a.jpg",
    "/dicom/2025-07-08/CT202506270155/87d52537455349338ba6b66df4f7d11b.jpg",
    "/dicom/2025-07-08/CT202506270155/cd253906e950445293ac45b6822c24e0.jpg",
    "/dicom/2025-07-08/CT202506270155/38e10a7490d847b5a2bcdb1c263c97d7.jpg",
    "/dicom/2025-07-08/CT202506270155/a847c071900f449e8767e5ff40b764bb.jpg",
    "/dicom/2025-07-08/CT202506270155/ed4d8e78f2d74f93bf7c0e46e644478b.jpg"
)

Write-Host "总共需要下载 $($imageUrls.Count) 张CT图片"

$successCount = 0
$failedCount = 0

for ($i = 0; $i -lt $imageUrls.Count; $i++) {
    $relativeUrl = $imageUrls[$i]
    $fullUrl = $BASE_URL + $relativeUrl
    $filename = "images\ct_{0:D3}.jpg" -f ($i + 1)
    
    # 检查文件是否已存在
    if (Test-Path $filename) {
        Write-Host "文件已存在，跳过: $filename" -ForegroundColor Yellow
        $successCount++
        continue
    }
    
    Write-Host "正在下载: $filename" -ForegroundColor Cyan

    # 使用curl下载文件
    $curlResult = curl.exe -s -L -o $filename $fullUrl --connect-timeout 30 --max-time 60

    # 检查文件是否下载成功
    if (Test-Path $filename) {
        $fileSize = (Get-Item $filename).Length
        if ($fileSize -gt 0) {
            Write-Host "✓ 下载成功: $filename ($fileSize bytes)" -ForegroundColor Green
            $successCount++
        } else {
            Write-Host "✗ 下载失败: $filename (文件大小为0)" -ForegroundColor Red
            Remove-Item $filename -ErrorAction SilentlyContinue
            $failedCount++
        }
    } else {
        Write-Host "✗ 下载失败: $filename (文件未创建)" -ForegroundColor Red
        $failedCount++
    }
    
    # 添加小延迟
    Start-Sleep -Milliseconds 500
}

Write-Host "`n下载完成!" -ForegroundColor Green
Write-Host "成功: $successCount 张" -ForegroundColor Green
Write-Host "失败: $failedCount 张" -ForegroundColor Red
Write-Host "图片保存在: $(Resolve-Path 'images')" -ForegroundColor Blue
