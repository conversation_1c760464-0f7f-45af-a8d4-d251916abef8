#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成完整的260张CT图片URL列表
"""

# 所有图片的URL数据
all_image_data = [
    # 1-40 (原有的)
    [
        "/dicom/2025-07-08/CT202506270155/aa3040123e9e4ad38d426e91d836d0b9.jpg",
        "/dicom/2025-07-08/CT202506270155/2182639d9426476c9e77b6a65e48c549.jpg",
        "/dicom/2025-07-08/CT202506270155/168dae35380d4f92b43ce7c363ad016e.jpg",
        "/dicom/2025-07-08/CT202506270155/ffdbf189699540609fef65f4a0e95cc4.jpg",
        "/dicom/2025-07-08/CT202506270155/ebc825503a904499a52d93864425e421.jpg",
        "/dicom/2025-07-08/CT202506270155/d0feae40a9c949e4ae36940fb0a3ffcf.jpg",
        "/dicom/2025-07-08/CT202506270155/e6a51f893d59433b94d56c0bfebc2afc.jpg",
        "/dicom/2025-07-08/CT202506270155/908808b42d5749c8bd874be4ee939b39.jpg",
        "/dicom/2025-07-08/CT202506270155/ca8bd6a21946453d90e6f73c025bcdad.jpg",
        "/dicom/2025-07-08/CT202506270155/d4bddf9319474a299adfbccbf0bed5e5.jpg",
        "/dicom/2025-07-08/CT202506270155/56b033ed74b74810aab2dfdc1672e50e.jpg",
        "/dicom/2025-07-08/CT202506270155/ca48b512ddc84ad79e9dfae2d9d3fd1d.jpg",
        "/dicom/2025-07-08/CT202506270155/48a13c87f9914d28a9f5e76ad2992e04.jpg",
        "/dicom/2025-07-08/CT202506270155/22d13dfae5e44be6b3656571511eb0f5.jpg",
        "/dicom/2025-07-08/CT202506270155/087920a394f645f097a12b9c024d4760.jpg",
        "/dicom/2025-07-08/CT202506270155/585e5802439d46c4ad4b065cef745d44.jpg",
        "/dicom/2025-07-08/CT202506270155/4f69fe799f1843b4a1c0b3b82d1b9cae.jpg",
        "/dicom/2025-07-08/CT202506270155/ede2e835bc8d40eda9aafaa87e5464b5.jpg",
        "/dicom/2025-07-08/CT202506270155/14f663d5337640c19cd93027699e1d54.jpg",
        "/dicom/2025-07-08/CT202506270155/c847a875a85a45abad1d363f8fe35bc1.jpg",
        "/dicom/2025-07-08/CT202506270155/3446de7edcb344c2a71cf95f548e173e.jpg",
        "/dicom/2025-07-08/CT202506270155/155888ba67834469bcf1989ead06322f.jpg",
        "/dicom/2025-07-08/CT202506270155/e5948c8b433648158a8befc922ffb032.jpg",
        "/dicom/2025-07-08/CT202506270155/4254bd7d5f554162b868010dd598450c.jpg",
        "/dicom/2025-07-08/CT202506270155/92978a9447ca4f5497806d2b44fad7b0.jpg",
        "/dicom/2025-07-08/CT202506270155/01ae8900295c49eb94365a14e5ac550d.jpg",
        "/dicom/2025-07-08/CT202506270155/04fc64640aed4c1f9fb8ac7fdde03826.jpg",
        "/dicom/2025-07-08/CT202506270155/9abb04756786417da9a958756c29984d.jpg",
        "/dicom/2025-07-08/CT202506270155/28500335b6864642bc4713a54d17426d.jpg",
        "/dicom/2025-07-08/CT202506270155/1494b9687b2d41e7852c3f1c5090e0ed.jpg",
        "/dicom/2025-07-08/CT202506270155/9d9011e27e5346888de2d3d569869794.jpg",
        "/dicom/2025-07-08/CT202506270155/20b245d44a734d5fa622a15746e740d2.jpg",
        "/dicom/2025-07-08/CT202506270155/bb922b1ab9484058b04dda92406c0752.jpg",
        "/dicom/2025-07-08/CT202506270155/60d0cab4253f4974b992d00c7f56b906.jpg",
        "/dicom/2025-07-08/CT202506270155/d936266ea1904b32bcabe1660d0c009a.jpg",
        "/dicom/2025-07-08/CT202506270155/87d52537455349338ba6b66df4f7d11b.jpg",
        "/dicom/2025-07-08/CT202506270155/cd253906e950445293ac45b6822c24e0.jpg",
        "/dicom/2025-07-08/CT202506270155/38e10a7490d847b5a2bcdb1c263c97d7.jpg",
        "/dicom/2025-07-08/CT202506270155/a847c071900f449e8767e5ff40b764bb.jpg",
        "/dicom/2025-07-08/CT202506270155/ed4d8e78f2d74f93bf7c0e46e644478b.jpg"
    ],
    # 41-60
    [
        "/dicom/2025-07-08/CT202506270155/cba7b8be0f8f45d6ba68732226c3ca91.jpg",
        "/dicom/2025-07-08/CT202506270155/a7f3cc2b57414d8b82f43638122c4c9e.jpg",
        "/dicom/2025-07-08/CT202506270155/42fafcda384d45ef81cb579e59dbe7b9.jpg",
        "/dicom/2025-07-08/CT202506270155/70feeace734643ad80cd22fdc98fa1cf.jpg",
        "/dicom/2025-07-08/CT202506270155/c708a9e9a00a4e6088e1c799c6e453c7.jpg",
        "/dicom/2025-07-08/CT202506270155/cef29739c4d04d00ae10f0987c84a4ab.jpg",
        "/dicom/2025-07-08/CT202506270155/466a3332c9f14e56b107f4eec888ba59.jpg",
        "/dicom/2025-07-08/CT202506270155/635e556e24ad4f789be43010ab430cec.jpg",
        "/dicom/2025-07-08/CT202506270155/2c507c95b8eb455da41d0f3bb0e94d83.jpg",
        "/dicom/2025-07-08/CT202506270155/89f21ddb0c7d4f98be79d686206524d9.jpg",
        "/dicom/2025-07-08/CT202506270155/b760971d8082463fa84870e5f5271d60.jpg",
        "/dicom/2025-07-08/CT202506270155/1b97804a01ac4c5386c60e52dea9f785.jpg",
        "/dicom/2025-07-08/CT202506270155/99fc55cb1b8d4be5ac3f8e66498cb0c4.jpg",
        "/dicom/2025-07-08/CT202506270155/43691b55cdeb4413a615b7f59695afe9.jpg",
        "/dicom/2025-07-08/CT202506270155/0e4f5868f42b40d4914f3d6652faa611.jpg",
        "/dicom/2025-07-08/CT202506270155/c17b61eb8f5847739d5654695263de8e.jpg",
        "/dicom/2025-07-08/CT202506270155/445eb7b5a55a424d8a43d72a184418bc.jpg",
        "/dicom/2025-07-08/CT202506270155/bdb82fb237904c41bc218a9dd246a2b8.jpg",
        "/dicom/2025-07-08/CT202506270155/c8ebaaae47704f9d992eeae3f59cd8a9.jpg",
        "/dicom/2025-07-08/CT202506270155/9c2ef5584abc4b1aa10b9095bad52720.jpg"
    ]
]

def generate_javascript_array():
    """生成JavaScript数组格式的URL列表"""
    all_urls = []
    for group in all_image_data:
        all_urls.extend(group)
    
    print("// 完整的260张CT图片URL列表")
    print("const imageUrls = [")
    
    for i, url in enumerate(all_urls):
        comment = ""
        if i == 0:
            comment = " // 1-40 (原有的)"
        elif i == 40:
            comment = " // 41-60 (新增的)"
        elif i == 60:
            comment = " // 61-80"
        
        print(f'    "{url}",{comment}')
    
    print("];")
    print(f"\n// 总计: {len(all_urls)} 张图片")

def main():
    print("生成完整的CT图片URL列表...")
    generate_javascript_array()

if __name__ == "__main__":
    main()
