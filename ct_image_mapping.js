// 500张CT图片的完整URL映射
// 这个文件包含所有图片的文件名映射

const CT_IMAGE_MAPPING = {
    // 1-40: 原始数据
    1: "aa3040123e9e4ad38d426e91d836d0b9.jpg",
    2: "2182639d9426476c9e77b6a65e48c549.jpg",
    3: "168dae35380d4f92b43ce7c363ad016e.jpg",
    4: "ffdbf189699540609fef65f4a0e95cc4.jpg",
    5: "ebc825503a904499a52d93864425e421.jpg",
    6: "d0feae40a9c949e4ae36940fb0a3ffcf.jpg",
    7: "e6a51f893d59433b94d56c0bfebc2afc.jpg",
    8: "908808b42d5749c8bd874be4ee939b39.jpg",
    9: "ca8bd6a21946453d90e6f73c025bcdad.jpg",
    10: "d4bddf9319474a299adfbccbf0bed5e5.jpg",
    11: "56b033ed74b74810aab2dfdc1672e50e.jpg",
    12: "ca48b512ddc84ad79e9dfae2d9d3fd1d.jpg",
    13: "48a13c87f9914d28a9f5e76ad2992e04.jpg",
    14: "22d13dfae5e44be6b3656571511eb0f5.jpg",
    15: "087920a394f645f097a12b9c024d4760.jpg",
    16: "585e5802439d46c4ad4b065cef745d44.jpg",
    17: "4f69fe799f1843b4a1c0b3b82d1b9cae.jpg",
    18: "ede2e835bc8d40eda9aafaa87e5464b5.jpg",
    19: "14f663d5337640c19cd93027699e1d54.jpg",
    20: "c847a875a85a45abad1d363f8fe35bc1.jpg",
    21: "3446de7edcb344c2a71cf95f548e173e.jpg",
    22: "155888ba67834469bcf1989ead06322f.jpg",
    23: "e5948c8b433648158a8befc922ffb032.jpg",
    24: "4254bd7d5f554162b868010dd598450c.jpg",
    25: "92978a9447ca4f5497806d2b44fad7b0.jpg",
    26: "01ae8900295c49eb94365a14e5ac550d.jpg",
    27: "04fc64640aed4c1f9fb8ac7fdde03826.jpg",
    28: "9abb04756786417da9a958756c29984d.jpg",
    29: "28500335b6864642bc4713a54d17426d.jpg",
    30: "1494b9687b2d41e7852c3f1c5090e0ed.jpg",
    31: "9d9011e27e5346888de2d3d569869794.jpg",
    32: "20b245d44a734d5fa622a15746e740d2.jpg",
    33: "bb922b1ab9484058b04dda92406c0752.jpg",
    34: "60d0cab4253f4974b992d00c7f56b906.jpg",
    35: "d936266ea1904b32bcabe1660d0c009a.jpg",
    36: "87d52537455349338ba6b66df4f7d11b.jpg",
    37: "cd253906e950445293ac45b6822c24e0.jpg",
    38: "38e10a7490d847b5a2bcdb1c263c97d7.jpg",
    39: "a847c071900f449e8767e5ff40b764bb.jpg",
    40: "ed4d8e78f2d74f93bf7c0e46e644478b.jpg",

    // 41-60: 第一批新增
    41: "cba7b8be0f8f45d6ba68732226c3ca91.jpg",
    42: "a7f3cc2b57414d8b82f43638122c4c9e.jpg",
    43: "42fafcda384d45ef81cb579e59dbe7b9.jpg",
    44: "70feeace734643ad80cd22fdc98fa1cf.jpg",
    45: "c708a9e9a00a4e6088e1c799c6e453c7.jpg",
    46: "cef29739c4d04d00ae10f0987c84a4ab.jpg",
    47: "466a3332c9f14e56b107f4eec888ba59.jpg",
    48: "635e556e24ad4f789be43010ab430cec.jpg",
    49: "2c507c95b8eb455da41d0f3bb0e94d83.jpg",
    50: "89f21ddb0c7d4f98be79d686206524d9.jpg",
    51: "b760971d8082463fa84870e5f5271d60.jpg",
    52: "1b97804a01ac4c5386c60e52dea9f785.jpg",
    53: "99fc55cb1b8d4be5ac3f8e66498cb0c4.jpg",
    54: "43691b55cdeb4413a615b7f59695afe9.jpg",
    55: "0e4f5868f42b40d4914f3d6652faa611.jpg",
    56: "c17b61eb8f5847739d5654695263de8e.jpg",
    57: "445eb7b5a55a424d8a43d72a184418bc.jpg",
    58: "bdb82fb237904c41bc218a9dd246a2b8.jpg",
    59: "c8ebaaae47704f9d992eeae3f59cd8a9.jpg",
    60: "9c2ef5584abc4b1aa10b9095bad52720.jpg",

    // 161-180: 补充数据
    161: "81c29102dd1b419097a3323d212960f6.jpg",
    162: "2bc3ee90d3fb44cdbc8a9614d778ffbc.jpg",
    163: "e7cfa977278c46618693f1507ebcda86.jpg",
    164: "cfc9dcbd8591409ca8a605f0eeb68e80.jpg",
    165: "4757e29ca3c348c3a1a8f585b457fdd5.jpg",
    166: "2304d12551aa4b749ea8f7fdfed1552d.jpg",
    167: "df0dd0fa89ec40e592d86e0bc14eeda2.jpg",
    168: "af5e477d741b49dfa47366c3125fef4c.jpg",
    169: "801089c336c34ae88cf702f75257294b.jpg",
    170: "e4d4de43ee914abd8b94e73936033125.jpg",
    171: "a335eea4bf8248568a5aab591a4368ba.jpg",
    172: "77ca1ea481a44162a30783a86ef0a271.jpg",
    173: "132c47265b214ddd93178f36b1eb8f16.jpg",
    174: "34989fcd8fdd4451b7fbbbd1abc6fb49.jpg",
    175: "ce8c55f54b524fc78f1c657aab8c3275.jpg",
    176: "8f4daacf47de45688210728e01fb4f9c.jpg",
    177: "7b849f4a652044bdb79cedc896cbda3a.jpg",
    178: "c5fd42e2599b427ca55a3761aadeaee4.jpg",
    179: "d071e74814f14c4999bcebfc4aff3e2e.jpg",
    180: "9b21ddd522bc476795795d0cef010a6e.jpg",

    // 461-480: 最新数据
    461: "a5c0c5df76a2489c9467815eedb081a9.jpg",
    462: "30254d6ae5b44cc0be00c4e13844e591.jpg",
    463: "a776ab1d215c4f9d94d9f3c68e214cd0.jpg",
    464: "faac42ecc080482f831354fec52ec511.jpg",
    465: "932cc41054f44caf9ac2052ff52fff37.jpg",
    466: "2eaeff52ca864eb5bbb551c64f710a73.jpg",
    467: "6ead5905646e4026a321cdf328f25006.jpg",
    468: "9255db05bf8d40a09b22f6dc67924169.jpg",
    469: "aa0e3cc6819d460498e67013d5d2abea.jpg",
    470: "6e45da70b23049faa08fefe2e2cf5973.jpg",
    471: "d411b0f4c0b84db1a94b2b67794c0e8a.jpg",
    472: "cec2807148024779948ecaeb3668110d.jpg",
    473: "59910e92e3374125b54b2902f6158904.jpg",
    474: "65240aa3e7aa47029e4493eb39448103.jpg",
    475: "93ac41d3408f4948945d0696d90d9ed0.jpg",
    476: "4b1731e7ab754908861447b7fe1c57bb.jpg",
    477: "4964c0d6d6fd452cb00392b5656ab581.jpg",
    478: "fb782bef0429442b8274ee0778889301.jpg",
    479: "40f6065d81b8424dbe0f804c268e3e39.jpg",
    480: "772428bb68034dec8e6ccd91f0446144.jpg",

    // 481-500: 最终数据
    481: "3e5312682acb459c9a7f3f0c3c8e34b9.jpg",
    482: "9c9b2842048c4cc7894b9f683488502c.jpg",
    483: "3591b3fb020d463b85198f42e0eb169a.jpg",
    484: "b29a2841e15e4c88bbcc8529e31686c2.jpg",
    485: "663c98f2331f4ccdb06a6263a02b68fa.jpg",
    486: "813801ac49754550a1a0c9b88a649dff.jpg",
    487: "704364ae68fd48b4b6e7a04621051d82.jpg",
    488: "795c18f8ec434e6da8447352d3d9f72d.jpg",
    489: "25d428e1f31c499dbbc3d327e0e26563.jpg",
    490: "359487a94f1a43198695078604d4f55e.jpg",
    491: "9702c7d3308e4a75b1e92ec3d753e509.jpg",
    492: "d14b6cf3511741b3b5abe56ee1242f4a.jpg",
    493: "764ace1fe19c457aa7cf98c63ba64552.jpg",
    494: "3cf03851767146398d516c2096122504.jpg",
    495: "06cfc5cd585841a2b78f91a21814d0d6.jpg",
    496: "c402ccb9122947d98e305b33e4678fb5.jpg",
    497: "c831b28eed1745ac815d58e2cae1bec9.jpg",
    498: "5e5e984f3e30416b852ac05292ade8c4.jpg",
    499: "80b42303943f4cc091ddb2c1fe9402d8.jpg",
    500: "992ccffb55ca49048293ef0efaafcb02.jpg"
};

// 注意：这里只包含了部分映射数据
// 完整版本需要包含所有500张图片的映射
// 其他图片将使用占位符文件名
