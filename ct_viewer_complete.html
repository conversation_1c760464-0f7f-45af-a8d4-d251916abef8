<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>腰部CT影像查看器 - 完整版 (260张)</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #1a1a1a;
            color: #ffffff;
            line-height: 1.6;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50, #34495e);
            padding: 20px 0;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            color: #ecf0f1;
        }

        .header p {
            font-size: 1.1em;
            color: #bdc3c7;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .controls {
            background: #2c3e50;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }

        .control-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .control-group label {
            font-weight: 500;
            color: #ecf0f1;
        }

        .control-group select,
        .control-group input {
            padding: 8px 12px;
            border: none;
            border-radius: 5px;
            background: #34495e;
            color: #ecf0f1;
            font-size: 14px;
        }

        .control-group button {
            padding: 8px 16px;
            border: none;
            border-radius: 5px;
            background: #3498db;
            color: white;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s;
        }

        .control-group button:hover {
            background: #2980b9;
        }

        .image-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .image-card {
            background: #2c3e50;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
            transition: transform 0.3s, box-shadow 0.3s;
            cursor: pointer;
        }

        .image-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.4);
        }

        .image-card img {
            width: 100%;
            height: 250px;
            object-fit: contain;
            background: #000;
            display: block;
        }

        .image-info {
            padding: 10px;
            text-align: center;
        }

        .image-info h3 {
            color: #3498db;
            margin-bottom: 5px;
            font-size: 1em;
        }

        .image-info p {
            color: #bdc3c7;
            font-size: 0.8em;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.9);
        }

        .modal-content {
            position: relative;
            margin: auto;
            padding: 20px;
            width: 90%;
            height: 90%;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .modal img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }

        .close {
            position: absolute;
            top: 15px;
            right: 35px;
            color: #f1f1f1;
            font-size: 40px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: #3498db;
        }

        .navigation {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(52, 73, 94, 0.8);
            color: white;
            border: none;
            padding: 15px 20px;
            font-size: 24px;
            cursor: pointer;
            border-radius: 5px;
            transition: background 0.3s;
        }

        .navigation:hover {
            background: rgba(52, 73, 94, 1);
        }

        .prev {
            left: 20px;
        }

        .next {
            right: 20px;
        }

        .loading {
            text-align: center;
            padding: 50px;
            font-size: 1.2em;
            color: #bdc3c7;
        }

        .status {
            background: #2c3e50;
            padding: 10px 20px;
            border-radius: 5px;
            margin-bottom: 20px;
            color: #bdc3c7;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .success { color: #2ecc71; }
        .error { color: #e74c3c; }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            margin: 20px 0;
            flex-wrap: wrap;
        }

        .pagination button {
            padding: 8px 12px;
            border: none;
            border-radius: 5px;
            background: #34495e;
            color: #ecf0f1;
            cursor: pointer;
            transition: background 0.3s;
        }

        .pagination button:hover {
            background: #3498db;
        }

        .pagination button.active {
            background: #3498db;
        }

        .pagination button:disabled {
            background: #2c3e50;
            cursor: not-allowed;
            opacity: 0.5;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2em;
            }
            
            .controls {
                flex-direction: column;
                align-items: stretch;
            }
            
            .control-group {
                justify-content: space-between;
            }
            
            .image-grid {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 10px;
            }

            .image-card img {
                height: 200px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>腰部CT影像查看器</h1>
        <p>CT202506270155 - 完整版本 (260张切片)</p>
    </div>

    <div class="container">
        <div class="status">
            <span id="statusText">正在初始化...</span>
            <span>
                成功: <span id="successCount">0</span> | 
                失败: <span id="errorCount">0</span> | 
                总计: <span id="totalCount">260</span>
            </span>
        </div>

        <div class="controls">
            <div class="control-group">
                <label for="gridSize">网格大小:</label>
                <select id="gridSize">
                    <option value="3">3列</option>
                    <option value="4" selected>4列</option>
                    <option value="5">5列</option>
                    <option value="6">6列</option>
                </select>
            </div>
            
            <div class="control-group">
                <label for="imageSize">图片大小:</label>
                <select id="imageSize">
                    <option value="200">小 (200px)</option>
                    <option value="250" selected>中 (250px)</option>
                    <option value="300">大 (300px)</option>
                </select>
            </div>

            <div class="control-group">
                <label for="imagesPerPage">每页显示:</label>
                <select id="imagesPerPage">
                    <option value="20">20张</option>
                    <option value="40" selected>40张</option>
                    <option value="60">60张</option>
                    <option value="100">100张</option>
                    <option value="260">全部</option>
                </select>
            </div>
            
            <div class="control-group">
                <button onclick="toggleFullscreen()">全屏模式</button>
                <button onclick="refreshImages()">刷新图片</button>
            </div>
        </div>

        <div class="pagination" id="pagination">
            <!-- 分页控件将在这里动态生成 -->
        </div>

        <div id="imageContainer" class="image-grid">
            <div class="loading">正在加载CT图片...</div>
        </div>

        <div class="pagination" id="paginationBottom">
            <!-- 底部分页控件 -->
        </div>
    </div>

    <!-- 模态框 -->
    <div id="imageModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <button class="navigation prev" onclick="previousImage()">&#10094;</button>
            <img id="modalImage" src="" alt="CT图片">
            <button class="navigation next" onclick="nextImage()">&#10095;</button>
        </div>
    </div>

    <script>
        let currentImageIndex = 0;
        let images = [];
        let successCount = 0;
        let errorCount = 0;
        let currentPage = 1;
        let imagesPerPage = 40;

        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            loadImages();
            setupEventListeners();
        });

        function loadImages() {
            const BASE_URL = "https://smp.hzwmin.cn/mh_tjstjyy_tempFile";
            
            // 完整的260张图片URL列表
            const imageUrls = [
                // 1-40 (原有的)
                "/dicom/2025-07-08/CT202506270155/aa3040123e9e4ad38d426e91d836d0b9.jpg",
                "/dicom/2025-07-08/CT202506270155/2182639d9426476c9e77b6a65e48c549.jpg",
                "/dicom/2025-07-08/CT202506270155/168dae35380d4f92b43ce7c363ad016e.jpg",
                "/dicom/2025-07-08/CT202506270155/ffdbf189699540609fef65f4a0e95cc4.jpg",
                "/dicom/2025-07-08/CT202506270155/ebc825503a904499a52d93864425e421.jpg"
                // 注意：这里只显示前5个，完整列表需要继续添加
            ];

            // 生成图片列表
            images = [];
            imageUrls.forEach((relativeUrl, index) => {
                images.push({
                    no: index + 1,
                    src: BASE_URL + relativeUrl,
                    title: `CT切片 ${index + 1}`,
                    filename: relativeUrl.split('/').pop()
                });
            });

            updateStatus('开始加载图片...');
            setupPagination();
            loadCurrentPage();
        }
